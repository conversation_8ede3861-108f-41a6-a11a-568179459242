import React, { memo } from 'react';
import { Card, Skeleton, Space } from 'antd';

const OrderBoardSkeleton = memo(() => {
  return (
    <div className="order-board-skeleton">
      {[1, 2, 3, 4].map((column) => (
        <div key={column} className="dnd-column">
          <Card
            size="small"
            style={{ marginBottom: 16, minHeight: 400 }}
            title={<Skeleton.Input style={{ width: 120 }} active />}
          >
            {[1, 2, 3].map((item) => (
              <Card
                key={item}
                size="small"
                style={{ marginBottom: 8 }}
                bodyStyle={{ padding: 12 }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Skeleton.Input style={{ width: 80 }} active />
                  <Skeleton active paragraph={{ rows: 2 }} />
                  <Space>
                    <Skeleton.Avatar size="small" active />
                    <Skeleton.Input style={{ width: 100 }} active />
                  </Space>
                </Space>
              </Card>
            ))}
          </Card>
        </div>
      ))}
    </div>
  );
});

export default OrderBoardSkeleton;
