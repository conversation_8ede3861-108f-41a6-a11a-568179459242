{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\order\\\\dnd\\\\List\\\\index.js\",\n  _s = $RefreshSig$();\nimport { SyncOutlined } from '@ant-design/icons';\nimport { Alert, Space, Tag } from 'antd';\nimport React, { memo } from 'react';\nimport { Droppable } from 'react-beautiful-dnd';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst List = /*#__PURE__*/_s( /*#__PURE__*/memo(_c = _s(({\n  children,\n  title,\n  name,\n  isDropDisabled,\n  total = 0,\n  loading = false,\n  reloadOrder\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(<PERSON><PERSON>, {\n      message: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n          size: 20,\n          style: {\n            cursor: 'pointer'\n          },\n          spin: loading,\n          onClick: reloadOrder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 15\n        }, this), t(title), /*#__PURE__*/_jsxDEV(Tag, {\n          children: loading ? /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 29\n          }, this) : total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this),\n      className: `mb-4 ${name}`,\n      style: {\n        textAlign: 'center',\n        fontSize: 16,\n        textTransform: 'capitalize'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Droppable, {\n      droppableId: name,\n      isDropDisabled: isDropDisabled,\n      children: provided => /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: provided.innerRef,\n        className: \"h-screen\",\n        style: {\n          opacity: isDropDisabled ? 0.6 : 1\n        },\n        children: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [children, provided.placeholder]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function () {\n  return [useTranslation];\n})), \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function () {\n  return [useTranslation];\n});\n_c2 = List;\nexport default List;\nvar _c, _c2;\n$RefreshReg$(_c, \"List$memo\");\n$RefreshReg$(_c2, \"List\");", "map": {"version": 3, "names": ["SyncOutlined", "<PERSON><PERSON>", "Space", "Tag", "React", "memo", "Droppable", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "List", "_s", "_c", "children", "title", "name", "isDropDisabled", "total", "loading", "reloadOrder", "t", "message", "size", "style", "cursor", "spin", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "textAlign", "fontSize", "textTransform", "droppableId", "provided", "ref", "innerRef", "opacity", "placeholder", "_c2", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/order/dnd/List/index.js"], "sourcesContent": ["import { SyncOutlined } from '@ant-design/icons';\nimport { Alert, Space, Tag } from 'antd';\nimport React, { memo } from 'react';\nimport { Droppable } from 'react-beautiful-dnd';\nimport { useTranslation } from 'react-i18next';\n\nconst List = memo(({\n  children,\n  title,\n  name,\n  isDropDisabled,\n  total = 0,\n  loading = false,\n  reloadOrder,\n}) => {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <Alert\n        message={\n          <Space>\n            {\n              <SyncOutlined\n                size={20}\n                style={{ cursor: 'pointer' }}\n                spin={loading}\n                onClick={reloadOrder}\n              />\n            }\n            {t(title)}\n            <Tag>{loading ? <SyncOutlined spin /> : total}</Tag>\n          </Space>\n        }\n        className={`mb-4 ${name}`}\n        style={{\n          textAlign: 'center',\n          fontSize: 16,\n          textTransform: 'capitalize',\n        }}\n      />\n      <Droppable droppableId={name} isDropDisabled={isDropDisabled}>\n        {(provided) => (\n          <div\n            ref={provided.innerRef}\n            className='h-screen'\n            style={{ opacity: isDropDisabled ? 0.6 : 1 }}\n          >\n            <>\n              {children}\n              {provided.placeholder}\n            </>\n          </div>\n        )}\n      </Droppable>\n    </>\n  );\n});\n\nexport default List;\n"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACxC,OAAOC,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,IAAI,gBAAAC,EAAA,eAAGR,IAAI,CAAAS,EAAA,GAAAD,EAAA,CAAC,CAAC;EACjBE,QAAQ;EACRC,KAAK;EACLC,IAAI;EACJC,cAAc;EACdC,KAAK,GAAG,CAAC;EACTC,OAAO,GAAG,KAAK;EACfC;AACF,CAAC,KAAK;EAAAR,EAAA;EACJ,MAAM;IAAES;EAAE,CAAC,GAAGf,cAAc,CAAC,CAAC;EAE9B,oBACEE,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACEN,OAAA,CAACR,KAAK;MACJsB,OAAO,eACLd,OAAA,CAACP,KAAK;QAAAa,QAAA,gBAEFN,OAAA,CAACT,YAAY;UACXwB,IAAI,EAAE,EAAG;UACTC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAC7BC,IAAI,EAAEP,OAAQ;UACdQ,OAAO,EAAEP;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,EAEHV,CAAC,CAACN,KAAK,CAAC,eACTP,OAAA,CAACN,GAAG;UAAAY,QAAA,EAAEK,OAAO,gBAAGX,OAAA,CAACT,YAAY;YAAC2B,IAAI;UAAA;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAGb;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACR;MACDC,SAAS,EAAG,QAAOhB,IAAK,EAAE;MAC1BQ,KAAK,EAAE;QACLS,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,EAAE;QACZC,aAAa,EAAE;MACjB;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFvB,OAAA,CAACH,SAAS;MAAC+B,WAAW,EAAEpB,IAAK;MAACC,cAAc,EAAEA,cAAe;MAAAH,QAAA,EACzDuB,QAAQ,iBACR7B,OAAA;QACE8B,GAAG,EAAED,QAAQ,CAACE,QAAS;QACvBP,SAAS,EAAC,UAAU;QACpBR,KAAK,EAAE;UAAEgB,OAAO,EAAEvB,cAAc,GAAG,GAAG,GAAG;QAAE,CAAE;QAAAH,QAAA,eAE7CN,OAAA,CAAAE,SAAA;UAAAI,QAAA,GACGA,QAAQ,EACRuB,QAAQ,CAACI,WAAW;QAAA,eACrB;MAAC;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA,eACZ,CAAC;AAEP,CAAC;EAAA,QA1CezB,cAAc;AAAA,EA0C7B,CAAC;EAAA,QA1CcA,cAAc;AAAA,EA0C5B;AAACoC,GAAA,GAnDG/B,IAAI;AAqDV,eAAeA,IAAI;AAAC,IAAAE,EAAA,EAAA6B,GAAA;AAAAC,YAAA,CAAA9B,EAAA;AAAA8B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}