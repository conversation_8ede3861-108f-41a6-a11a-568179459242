{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\report-overview\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Card, Col, Row, Space, Typography, Table, Tag, Button, DatePicker, Spin, Divider, Select } from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport { BarChartOutlined, LineChartOutlined } from '@ant-design/icons';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport { fetchReportOverviewCart, fetchReportOverviewCategories, fetchReportOverviewProducts } from '../../redux/slices/report/overview';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { useTranslation } from 'react-i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport ChartWidget from '../../components/chart-widget';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst ReportOverview = () => {\n  _s();\n  var _carts$chart_price, _carts$chart_price2, _carts$chart_count, _carts$chart_price3;\n  const {\n    t\n  } = useTranslation();\n  const location = useLocation();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const {\n    date_from,\n    date_to,\n    by_time,\n    chart,\n    handleDateRange,\n    options,\n    handleByTime,\n    chart_type,\n    setChartType\n  } = useContext(ReportContext);\n  const {\n    loading,\n    carts,\n    products,\n    categories\n  } = useSelector(state => state.overviewReport, shallowEqual);\n  const {\n    defaultCurrency\n  } = useSelector(state => state.currency, shallowEqual);\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const columns = [{\n    title: t('title'),\n    dataIndex: 'title',\n    key: 'title'\n    // render: (text) => <a>{text}</a>,\n  }, {\n    title: t('item.sold'),\n    dataIndex: 'quantity',\n    key: 'quantity'\n  }, {\n    title: t('net.sales'),\n    dataIndex: 'total_price',\n    key: 'total_price',\n    render: price => numberToPrice(price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position)\n  }, {\n    title: t('orders'),\n    dataIndex: 'count',\n    key: 'count'\n  }];\n  const performance = [{\n    title: t('total.sales'),\n    qty: 'delivered_sum',\n    percent: '5',\n    price: true\n  }, {\n    title: t('orders'),\n    qty: 'count',\n    percent: '5',\n    price: false\n  }, {\n    title: t('canceled.orders.price'),\n    qty: 'canceled_sum',\n    percent: '5',\n    price: true\n  }, {\n    title: t('total.tax'),\n    qty: 'tax',\n    percent: '5',\n    price: true\n  }, {\n    title: t('delivered.avg'),\n    qty: 'delivered_avg',\n    percent: '5',\n    price: true\n  }, {\n    title: t('delivery.fee'),\n    qty: 'delivery_fee',\n    percent: '5',\n    price: true\n  }];\n  const fetchProducts = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage\n    };\n    dispatch(fetchReportOverviewProducts(params));\n  };\n  const fetchCategories = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage\n    };\n    dispatch(fetchReportOverviewCategories(params));\n  };\n  const fetchOverview = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchReportOverviewCart(params));\n  };\n  const onProductPaginationChange = pagination => {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    fetchProducts(page, perPage);\n  };\n  const onCategoryPaginationChange = pagination => {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    fetchProducts(page, perPage);\n  };\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchOverview();\n      fetchProducts();\n      fetchCategories();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n  useDidUpdate(() => {\n    fetchOverview();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n  useDidUpdate(() => {\n    fetchProducts();\n  }, [date_to, by_time, date_from]);\n  useDidUpdate(() => {\n    fetchCategories();\n  }, [date_to, by_time, date_from]);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    size: \"large\",\n    spinning: loading,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            ...configureRangePicker(),\n            defaultValue: [moment(date_from), moment(date_to)],\n            onChange: handleDateRange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      orientation: \"left\",\n      children: t('performance')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: performance === null || performance === void 0 ? void 0 : performance.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/report/revenue\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-5\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: 24,\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Title, {\n                    level: 2,\n                    children: item.price ? numberToPrice(carts[item.qty], defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position) : carts[item.qty]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, item.title || `performance-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 20,\n        children: /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: t('charts')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 4,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: 100\n            },\n            onChange: handleByTime,\n            options: options,\n            defaultValue: by_time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            type: \"vertical\",\n            style: {\n              height: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {\n              style: {\n                fontSize: '22px',\n                cursor: 'pointer',\n                color: chart_type === 'line' ? 'green' : ''\n              },\n              onClick: () => setChartType('line')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BarChartOutlined, {\n              style: {\n                fontSize: '22px',\n                cursor: 'pointer',\n                color: chart_type === 'bar' ? 'green' : ''\n              },\n              onClick: () => setChartType('bar')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: t('net.sales'),\n          children: /*#__PURE__*/_jsxDEV(ChartWidget, {\n            type: chart_type,\n            series: [{\n              name: t('orders'),\n              data: (carts === null || carts === void 0 ? void 0 : (_carts$chart_price = carts.chart_price) === null || _carts$chart_price === void 0 ? void 0 : _carts$chart_price.map(item => item.delivered_sum)) || []\n            }],\n            xAxis: (carts === null || carts === void 0 ? void 0 : (_carts$chart_price2 = carts.chart_price) === null || _carts$chart_price2 === void 0 ? void 0 : _carts$chart_price2.map(item => item.time)) || []\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: t('orders'),\n          children: /*#__PURE__*/_jsxDEV(ChartWidget, {\n            type: chart_type,\n            series: [{\n              name: t('orders'),\n              data: (carts === null || carts === void 0 ? void 0 : (_carts$chart_count = carts.chart_count) === null || _carts$chart_count === void 0 ? void 0 : _carts$chart_count.map(item => item.count)) || []\n            }],\n            xAxis: (carts === null || carts === void 0 ? void 0 : (_carts$chart_price3 = carts.chart_price) === null || _carts$chart_price3 === void 0 ? void 0 : _carts$chart_price3.map(item => item.time)) || []\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      orientation: \"left\",\n      children: t('leaderboards')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: t('top.categories'),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            scroll: {\n              x: true\n            },\n            onChange: onCategoryPaginationChange,\n            pagination: {\n              pageSize: categories === null || categories === void 0 ? void 0 : categories.per_page,\n              page: (categories === null || categories === void 0 ? void 0 : categories.current_page) || 1,\n              total: categories === null || categories === void 0 ? void 0 : categories.total,\n              defaultCurrent: 1\n            },\n            columns: columns,\n            dataSource: categories === null || categories === void 0 ? void 0 : categories.data,\n            rowKey: row => row.id || row.uuid || `category-${row.title}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: t('top.products'),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            scroll: {\n              x: true\n            },\n            onChange: onProductPaginationChange,\n            pagination: {\n              pageSize: products === null || products === void 0 ? void 0 : products.per_page,\n              page: (products === null || products === void 0 ? void 0 : products.current_page) || 1,\n              total: products === null || products === void 0 ? void 0 : products.total,\n              defaultCurrent: 1\n            },\n            columns: columns,\n            dataSource: products === null || products === void 0 ? void 0 : products.data,\n            rowKey: row => row.id || row.uuid || `product-${row.title}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportOverview, \"4IqRoR4CMjlICKoElN9Ix7qInEA=\", false, function () {\n  return [useTranslation, useLocation, useSelector, useSelector, useSelector, useDispatch, useDidUpdate, useDidUpdate, useDidUpdate];\n});\n_c = ReportOverview;\nexport default ReportOverview;\nvar _c;\n$RefreshReg$(_c, \"ReportOverview\");", "map": {"version": 3, "names": ["Card", "Col", "Row", "Space", "Typography", "Table", "Tag", "<PERSON><PERSON>", "DatePicker", "Spin", "Divider", "Select", "React", "useContext", "useEffect", "useState", "BarChartOutlined", "LineChartOutlined", "disable<PERSON><PERSON><PERSON><PERSON>", "shallowEqual", "useDispatch", "useSelector", "moment", "ReportContext", "configureRangePicker", "fetchReportOverviewCart", "fetchReportOverviewCategories", "fetchReportOverviewProducts", "useDidUpdate", "Link", "useLocation", "QueryString", "useTranslation", "numberToPrice", "ChartWidget", "jsxDEV", "_jsxDEV", "Text", "Title", "RangePicker", "ReportOverview", "_s", "_carts$chart_price", "_carts$chart_price2", "_carts$chart_count", "_carts$chart_price3", "t", "location", "category_id", "parse", "search", "product_id", "date_from", "date_to", "by_time", "chart", "handleDateRange", "options", "handleByTime", "chart_type", "setChartType", "loading", "carts", "products", "categories", "state", "overviewReport", "defaultCurrency", "currency", "activeMenu", "menu", "dispatch", "columns", "title", "dataIndex", "key", "render", "price", "symbol", "position", "performance", "qty", "percent", "fetchProducts", "page", "perPage", "params", "type", "fetchCategories", "fetchOverview", "onProductPaginationChange", "pagination", "pageSize", "current", "onCategoryPaginationChange", "refetch", "size", "spinning", "children", "gutter", "className", "span", "defaultValue", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "orientation", "map", "item", "index", "to", "level", "style", "width", "height", "fontSize", "cursor", "color", "onClick", "series", "name", "data", "chart_price", "delivered_sum", "xAxis", "time", "chart_count", "count", "scroll", "x", "per_page", "current_page", "total", "defaultCurrent", "dataSource", "<PERSON><PERSON><PERSON>", "row", "id", "uuid", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/report-overview/index.js"], "sourcesContent": ["import {\n  <PERSON>,\n  Col,\n  Row,\n  Space,\n  Typography,\n  Table,\n  Tag,\n  Button,\n  DatePicker,\n  Spin,\n  Divider,\n  Select,\n} from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport { Bar<PERSON>hartOutlined, LineChartOutlined } from '@ant-design/icons';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport {\n  fetchReportOverviewCart,\n  fetchReportOverviewCategories,\n  fetchReportOverviewProducts,\n} from '../../redux/slices/report/overview';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { useTranslation } from 'react-i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport ChartWidget from '../../components/chart-widget';\nconst { Text, Title } = Typography;\nconst { RangePicker } = DatePicker;\nconst ReportOverview = () => {\n  const { t } = useTranslation();\n  const location = useLocation();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const {\n    date_from,\n    date_to,\n    by_time,\n    chart,\n    handleDateRange,\n    options,\n    handleByTime,\n    chart_type,\n    setChartType,\n  } = useContext(ReportContext);\n  const { loading, carts, products, categories } = useSelector(\n    (state) => state.overviewReport,\n    shallowEqual,\n  );\n  const { defaultCurrency } = useSelector(\n    (state) => state.currency,\n    shallowEqual,\n  );\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const columns = [\n    {\n      title: t('title'),\n      dataIndex: 'title',\n      key: 'title',\n      // render: (text) => <a>{text}</a>,\n    },\n    {\n      title: t('item.sold'),\n      dataIndex: 'quantity',\n      key: 'quantity',\n    },\n    {\n      title: t('net.sales'),\n      dataIndex: 'total_price',\n      key: 'total_price',\n      render: (price) =>\n        numberToPrice(\n          price,\n          defaultCurrency?.symbol,\n          defaultCurrency?.position,\n        ),\n    },\n    {\n      title: t('orders'),\n      dataIndex: 'count',\n      key: 'count',\n    },\n  ];\n  const performance = [\n    {\n      title: t('total.sales'),\n      qty: 'delivered_sum',\n      percent: '5',\n      price: true,\n    },\n    {\n      title: t('orders'),\n      qty: 'count',\n      percent: '5',\n      price: false,\n    },\n    {\n      title: t('canceled.orders.price'),\n      qty: 'canceled_sum',\n      percent: '5',\n      price: true,\n    },\n    {\n      title: t('total.tax'),\n      qty: 'tax',\n      percent: '5',\n      price: true,\n    },\n    {\n      title: t('delivered.avg'),\n      qty: 'delivered_avg',\n      percent: '5',\n      price: true,\n    },\n    {\n      title: t('delivery.fee'),\n      qty: 'delivery_fee',\n      percent: '5',\n      price: true,\n    },\n  ];\n\n  const fetchProducts = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n    };\n    dispatch(fetchReportOverviewProducts(params));\n  };\n\n  const fetchCategories = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n    };\n    dispatch(fetchReportOverviewCategories(params));\n  };\n\n  const fetchOverview = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    dispatch(fetchReportOverviewCart(params));\n  };\n\n  const onProductPaginationChange = (pagination) => {\n    const { pageSize: perPage, current: page } = pagination;\n    fetchProducts(page, perPage);\n  };\n\n  const onCategoryPaginationChange = (pagination) => {\n    const { pageSize: perPage, current: page } = pagination;\n    fetchProducts(page, perPage);\n  };\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchOverview();\n      fetchProducts();\n      fetchCategories();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n\n  useDidUpdate(() => {\n    fetchOverview();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n\n  useDidUpdate(() => {\n    fetchProducts();\n  }, [date_to, by_time, date_from]);\n\n  useDidUpdate(() => {\n    fetchCategories();\n  }, [date_to, by_time, date_from]);\n\n  return (\n    <Spin size='large' spinning={loading}>\n      <Row gutter={24} className='mb-4'>\n        <Col span={12}>\n          <Space size='large'>\n            <RangePicker\n              {...configureRangePicker()}\n              defaultValue={[moment(date_from), moment(date_to)]}\n              onChange={handleDateRange}\n            />\n          </Space>\n        </Col>\n      </Row>\n      <Divider orientation='left'>{t('performance')}</Divider>\n      <Row gutter={24}>\n        {performance?.map((item, index) => {\n          return (\n            <Col span={6} key={item.title || `performance-${index}`}>\n              <Link to='/report/revenue'>\n                <Card>\n                  <Row className='mb-5'>\n                    <Col>\n                      <Text>{item.title}</Text>\n                    </Col>\n                  </Row>\n                  <Row gutter={24}>\n                    <Col span={12}>\n                      <Title level={2}>\n                        {item.price\n                          ? numberToPrice(\n                              carts[item.qty],\n                              defaultCurrency?.symbol,\n                              defaultCurrency?.position,\n                            )\n                          : carts[item.qty]}\n                      </Title>\n                    </Col>\n                  </Row>\n                </Card>\n              </Link>\n            </Col>\n          );\n        })}\n      </Row>\n      <Row gutter={24} className='mb-2'>\n        <Col span={20}>\n          <Divider orientation='left'>{t('charts')}</Divider>\n        </Col>\n        <Col span={4}>\n          <div className='d-flex'>\n            <Select\n              style={{ width: 100 }}\n              onChange={handleByTime}\n              options={options}\n              defaultValue={by_time}\n            />\n\n            <Divider type='vertical' style={{ height: '100%' }} />\n            <Space>\n              <LineChartOutlined\n                style={{\n                  fontSize: '22px',\n                  cursor: 'pointer',\n                  color: chart_type === 'line' ? 'green' : '',\n                }}\n                onClick={() => setChartType('line')}\n              />\n              <BarChartOutlined\n                style={{\n                  fontSize: '22px',\n                  cursor: 'pointer',\n                  color: chart_type === 'bar' ? 'green' : '',\n                }}\n                onClick={() => setChartType('bar')}\n              />\n            </Space>\n          </div>\n        </Col>\n      </Row>\n      <Row gutter={24}>\n        <Col span={12}>\n          <Card title={t('net.sales')}>\n            <ChartWidget\n              type={chart_type}\n              series={[\n                {\n                  name: t('orders'),\n                  data: carts?.chart_price?.map((item) => item.delivered_sum) || [],\n                },\n              ]}\n              xAxis={carts?.chart_price?.map((item) => item.time) || []}\n            />\n          </Card>\n        </Col>\n        <Col span={12}>\n          <Card title={t('orders')}>\n            <ChartWidget\n              type={chart_type}\n              series={[\n                {\n                  name: t('orders'),\n                  data: carts?.chart_count?.map((item) => item.count) || [],\n                },\n              ]}\n              xAxis={carts?.chart_price?.map((item) => item.time) || []}\n            />\n          </Card>\n        </Col>\n      </Row>\n      <Divider orientation='left'>{t('leaderboards')}</Divider>\n      <Row gutter={24}>\n        <Col span={12}>\n          <Card title={t('top.categories')}>\n            <Table\n              scroll={{ x: true }}\n              onChange={onCategoryPaginationChange}\n              pagination={{\n                pageSize: categories?.per_page,\n                page: categories?.current_page || 1,\n                total: categories?.total,\n                defaultCurrent: 1,\n              }}\n              columns={columns}\n              dataSource={categories?.data}\n              rowKey={(row) => row.id || row.uuid || `category-${row.title}`}\n            />\n          </Card>\n        </Col>\n        <Col span={12}>\n          <Card title={t('top.products')}>\n            <Table\n              scroll={{ x: true }}\n              onChange={onProductPaginationChange}\n              pagination={{\n                pageSize: products?.per_page,\n                page: products?.current_page || 1,\n                total: products?.total,\n                defaultCurrent: 1,\n              }}\n              columns={columns}\n              dataSource={products?.data}\n              rowKey={(row) => row.id || row.uuid || `product-${row.title}`}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </Spin>\n  );\n};\n\nexport default ReportOverview;\n"], "mappings": ";;AAAA,SACEA,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,MAAM,QACD,MAAM;AACb,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,mBAAmB;AACvE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SACEC,uBAAuB,EACvBC,6BAA6B,EAC7BC,2BAA2B,QACtB,oCAAoC;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,WAAW,MAAM,IAAI;AAC5B,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,WAAW,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACxD,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAGlC,UAAU;AAClC,MAAM;EAAEmC;AAAY,CAAC,GAAG/B,UAAU;AAClC,MAAMgC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA;EAC3B,MAAM;IAAEC;EAAE,CAAC,GAAGd,cAAc,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,WAAW,GAAGjB,WAAW,CAACkB,KAAK,CAACF,QAAQ,CAACG,MAAM,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC;EAC1E,MAAMC,UAAU,GAAGpB,WAAW,CAACkB,KAAK,CAACF,QAAQ,CAACG,MAAM,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC;EACxE,MAAM;IACJE,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,KAAK;IACLC,eAAe;IACfC,OAAO;IACPC,YAAY;IACZC,UAAU;IACVC;EACF,CAAC,GAAG/C,UAAU,CAACU,aAAa,CAAC;EAC7B,MAAM;IAAEsC,OAAO;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAG3C,WAAW,CACzD4C,KAAK,IAAKA,KAAK,CAACC,cAAc,EAC/B/C,YACF,CAAC;EACD,MAAM;IAAEgD;EAAgB,CAAC,GAAG9C,WAAW,CACpC4C,KAAK,IAAKA,KAAK,CAACG,QAAQ,EACzBjD,YACF,CAAC;EACD,MAAM;IAAEkD;EAAW,CAAC,GAAGhD,WAAW,CAAE4C,KAAK,IAAKA,KAAK,CAACK,IAAI,EAAEnD,YAAY,CAAC;EACvE,MAAMoD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAMoD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE3B,CAAC,CAAC,OAAO,CAAC;IACjB4B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;IACL;EACF,CAAC,EACD;IACEF,KAAK,EAAE3B,CAAC,CAAC,WAAW,CAAC;IACrB4B,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE3B,CAAC,CAAC,WAAW,CAAC;IACrB4B,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGC,KAAK,IACZ5C,aAAa,CACX4C,KAAK,EACLV,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEW,MAAM,EACvBX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEY,QACnB;EACJ,CAAC,EACD;IACEN,KAAK,EAAE3B,CAAC,CAAC,QAAQ,CAAC;IAClB4B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,CACF;EACD,MAAMK,WAAW,GAAG,CAClB;IACEP,KAAK,EAAE3B,CAAC,CAAC,aAAa,CAAC;IACvBmC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE3B,CAAC,CAAC,QAAQ,CAAC;IAClBmC,GAAG,EAAE,OAAO;IACZC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE3B,CAAC,CAAC,uBAAuB,CAAC;IACjCmC,GAAG,EAAE,cAAc;IACnBC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE3B,CAAC,CAAC,WAAW,CAAC;IACrBmC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE3B,CAAC,CAAC,eAAe,CAAC;IACzBmC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE3B,CAAC,CAAC,cAAc,CAAC;IACxBmC,GAAG,EAAE,cAAc;IACnBC,OAAO,EAAE,GAAG;IACZL,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMM,aAAa,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;IACvC,MAAMC,MAAM,GAAG;MACblC,SAAS;MACTC,OAAO;MACPkC,IAAI,EAAEjC,OAAO;MACb8B,IAAI;MACJC;IACF,CAAC;IACDd,QAAQ,CAAC5C,2BAA2B,CAAC2D,MAAM,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,eAAe,GAAGA,CAACJ,IAAI,EAAEC,OAAO,KAAK;IACzC,MAAMC,MAAM,GAAG;MACblC,SAAS;MACTC,OAAO;MACPkC,IAAI,EAAEjC,OAAO;MACb8B,IAAI;MACJC;IACF,CAAC;IACDd,QAAQ,CAAC7C,6BAA6B,CAAC4D,MAAM,CAAC,CAAC;EACjD,CAAC;EAED,MAAMG,aAAa,GAAGA,CAACL,IAAI,EAAEC,OAAO,KAAK;IACvC,MAAMC,MAAM,GAAG;MACblC,SAAS;MACTC,OAAO;MACPkC,IAAI,EAAEjC,OAAO;MACb8B,IAAI;MACJC;IACF,CAAC;IACD,IAAIrC,WAAW,EAAEsC,MAAM,CAACtB,UAAU,GAAG,CAAChB,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAEmC,MAAM,CAACvB,QAAQ,GAAG,CAACZ,UAAU,CAAC;IAC9CoB,QAAQ,CAAC9C,uBAAuB,CAAC6D,MAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMI,yBAAyB,GAAIC,UAAU,IAAK;IAChD,MAAM;MAAEC,QAAQ,EAAEP,OAAO;MAAEQ,OAAO,EAAET;IAAK,CAAC,GAAGO,UAAU;IACvDR,aAAa,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC9B,CAAC;EAED,MAAMS,0BAA0B,GAAIH,UAAU,IAAK;IACjD,MAAM;MAAEC,QAAQ,EAAEP,OAAO;MAAEQ,OAAO,EAAET;IAAK,CAAC,GAAGO,UAAU;IACvDR,aAAa,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC9B,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACd,IAAIuD,UAAU,CAAC0B,OAAO,EAAE;MACtBN,aAAa,CAAC,CAAC;MACfN,aAAa,CAAC,CAAC;MACfK,eAAe,CAAC,CAAC;MACjBjB,QAAQ,CAACrD,cAAc,CAACmD,UAAU,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,UAAU,CAAC0B,OAAO,CAAC,CAAC;EAExBnE,YAAY,CAAC,MAAM;IACjB6D,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACpC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEP,WAAW,EAAEG,UAAU,EAAEC,SAAS,CAAC,CAAC;EAEjExB,YAAY,CAAC,MAAM;IACjBuD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC9B,OAAO,EAAEC,OAAO,EAAEF,SAAS,CAAC,CAAC;EAEjCxB,YAAY,CAAC,MAAM;IACjB4D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnC,OAAO,EAAEC,OAAO,EAAEF,SAAS,CAAC,CAAC;EAEjC,oBACEhB,OAAA,CAAC3B,IAAI;IAACuF,IAAI,EAAC,OAAO;IAACC,QAAQ,EAAEpC,OAAQ;IAAAqC,QAAA,gBACnC9D,OAAA,CAAClC,GAAG;MAACiG,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,eAC/B9D,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAACjC,KAAK;UAAC6F,IAAI,EAAC,OAAO;UAAAE,QAAA,eACjB9D,OAAA,CAACG,WAAW;YAAA,GACNf,oBAAoB,CAAC,CAAC;YAC1B8E,YAAY,EAAE,CAAChF,MAAM,CAAC8B,SAAS,CAAC,EAAE9B,MAAM,CAAC+B,OAAO,CAAC,CAAE;YACnDkD,QAAQ,EAAE/C;UAAgB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvE,OAAA,CAAC1B,OAAO;MAACkG,WAAW,EAAC,MAAM;MAAAV,QAAA,EAAEpD,CAAC,CAAC,aAAa;IAAC;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC,eACxDvE,OAAA,CAAClC,GAAG;MAACiG,MAAM,EAAE,EAAG;MAAAD,QAAA,EACblB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QACjC,oBACE3E,OAAA,CAACnC,GAAG;UAACoG,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX9D,OAAA,CAACP,IAAI;YAACmF,EAAE,EAAC,iBAAiB;YAAAd,QAAA,eACxB9D,OAAA,CAACpC,IAAI;cAAAkG,QAAA,gBACH9D,OAAA,CAAClC,GAAG;gBAACkG,SAAS,EAAC,MAAM;gBAAAF,QAAA,eACnB9D,OAAA,CAACnC,GAAG;kBAAAiG,QAAA,eACF9D,OAAA,CAACC,IAAI;oBAAA6D,QAAA,EAAEY,IAAI,CAACrC;kBAAK;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvE,OAAA,CAAClC,GAAG;gBAACiG,MAAM,EAAE,EAAG;gBAAAD,QAAA,eACd9D,OAAA,CAACnC,GAAG;kBAACoG,IAAI,EAAE,EAAG;kBAAAH,QAAA,eACZ9D,OAAA,CAACE,KAAK;oBAAC2E,KAAK,EAAE,CAAE;oBAAAf,QAAA,EACbY,IAAI,CAACjC,KAAK,GACP5C,aAAa,CACX6B,KAAK,CAACgD,IAAI,CAAC7B,GAAG,CAAC,EACfd,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEW,MAAM,EACvBX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEY,QACnB,CAAC,GACDjB,KAAK,CAACgD,IAAI,CAAC7B,GAAG;kBAAC;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAtBUG,IAAI,CAACrC,KAAK,IAAK,eAAcsC,KAAM,EAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBlD,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNvE,OAAA,CAAClC,GAAG;MAACiG,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAC/B9D,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAAC1B,OAAO;UAACkG,WAAW,EAAC,MAAM;UAAAV,QAAA,EAAEpD,CAAC,CAAC,QAAQ;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNvE,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,CAAE;QAAAH,QAAA,eACX9D,OAAA;UAAKgE,SAAS,EAAC,QAAQ;UAAAF,QAAA,gBACrB9D,OAAA,CAACzB,MAAM;YACLuG,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YACtBZ,QAAQ,EAAE7C,YAAa;YACvBD,OAAO,EAAEA,OAAQ;YACjB6C,YAAY,EAAEhD;UAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEFvE,OAAA,CAAC1B,OAAO;YAAC6E,IAAI,EAAC,UAAU;YAAC2B,KAAK,EAAE;cAAEE,MAAM,EAAE;YAAO;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDvE,OAAA,CAACjC,KAAK;YAAA+F,QAAA,gBACJ9D,OAAA,CAACnB,iBAAiB;cAChBiG,KAAK,EAAE;gBACLG,QAAQ,EAAE,MAAM;gBAChBC,MAAM,EAAE,SAAS;gBACjBC,KAAK,EAAE5D,UAAU,KAAK,MAAM,GAAG,OAAO,GAAG;cAC3C,CAAE;cACF6D,OAAO,EAAEA,CAAA,KAAM5D,YAAY,CAAC,MAAM;YAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFvE,OAAA,CAACpB,gBAAgB;cACfkG,KAAK,EAAE;gBACLG,QAAQ,EAAE,MAAM;gBAChBC,MAAM,EAAE,SAAS;gBACjBC,KAAK,EAAE5D,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG;cAC1C,CAAE;cACF6D,OAAO,EAAEA,CAAA,KAAM5D,YAAY,CAAC,KAAK;YAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvE,OAAA,CAAClC,GAAG;MAACiG,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACd9D,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAACpC,IAAI;UAACyE,KAAK,EAAE3B,CAAC,CAAC,WAAW,CAAE;UAAAoD,QAAA,eAC1B9D,OAAA,CAACF,WAAW;YACVqD,IAAI,EAAE5B,UAAW;YACjB8D,MAAM,EAAE,CACN;cACEC,IAAI,EAAE5E,CAAC,CAAC,QAAQ,CAAC;cACjB6E,IAAI,EAAE,CAAA7D,KAAK,aAALA,KAAK,wBAAApB,kBAAA,GAALoB,KAAK,CAAE8D,WAAW,cAAAlF,kBAAA,uBAAlBA,kBAAA,CAAoBmE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACe,aAAa,CAAC,KAAI;YACjE,CAAC,CACD;YACFC,KAAK,EAAE,CAAAhE,KAAK,aAALA,KAAK,wBAAAnB,mBAAA,GAALmB,KAAK,CAAE8D,WAAW,cAAAjF,mBAAA,uBAAlBA,mBAAA,CAAoBkE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACiB,IAAI,CAAC,KAAI;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAACpC,IAAI;UAACyE,KAAK,EAAE3B,CAAC,CAAC,QAAQ,CAAE;UAAAoD,QAAA,eACvB9D,OAAA,CAACF,WAAW;YACVqD,IAAI,EAAE5B,UAAW;YACjB8D,MAAM,EAAE,CACN;cACEC,IAAI,EAAE5E,CAAC,CAAC,QAAQ,CAAC;cACjB6E,IAAI,EAAE,CAAA7D,KAAK,aAALA,KAAK,wBAAAlB,kBAAA,GAALkB,KAAK,CAAEkE,WAAW,cAAApF,kBAAA,uBAAlBA,kBAAA,CAAoBiE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACmB,KAAK,CAAC,KAAI;YACzD,CAAC,CACD;YACFH,KAAK,EAAE,CAAAhE,KAAK,aAALA,KAAK,wBAAAjB,mBAAA,GAALiB,KAAK,CAAE8D,WAAW,cAAA/E,mBAAA,uBAAlBA,mBAAA,CAAoBgE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACiB,IAAI,CAAC,KAAI;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvE,OAAA,CAAC1B,OAAO;MAACkG,WAAW,EAAC,MAAM;MAAAV,QAAA,EAAEpD,CAAC,CAAC,cAAc;IAAC;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC,eACzDvE,OAAA,CAAClC,GAAG;MAACiG,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACd9D,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAACpC,IAAI;UAACyE,KAAK,EAAE3B,CAAC,CAAC,gBAAgB,CAAE;UAAAoD,QAAA,eAC/B9D,OAAA,CAAC/B,KAAK;YACJ6H,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK,CAAE;YACpB5B,QAAQ,EAAET,0BAA2B;YACrCH,UAAU,EAAE;cACVC,QAAQ,EAAE5B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoE,QAAQ;cAC9BhD,IAAI,EAAE,CAAApB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqE,YAAY,KAAI,CAAC;cACnCC,KAAK,EAAEtE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEsE,KAAK;cACxBC,cAAc,EAAE;YAClB,CAAE;YACF/D,OAAO,EAAEA,OAAQ;YACjBgE,UAAU,EAAExE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE2D,IAAK;YAC7Bc,MAAM,EAAGC,GAAG,IAAKA,GAAG,CAACC,EAAE,IAAID,GAAG,CAACE,IAAI,IAAK,YAAWF,GAAG,CAACjE,KAAM;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvE,OAAA,CAACnC,GAAG;QAACoG,IAAI,EAAE,EAAG;QAAAH,QAAA,eACZ9D,OAAA,CAACpC,IAAI;UAACyE,KAAK,EAAE3B,CAAC,CAAC,cAAc,CAAE;UAAAoD,QAAA,eAC7B9D,OAAA,CAAC/B,KAAK;YACJ6H,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK,CAAE;YACpB5B,QAAQ,EAAEb,yBAA0B;YACpCC,UAAU,EAAE;cACVC,QAAQ,EAAE7B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqE,QAAQ;cAC5BhD,IAAI,EAAE,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsE,YAAY,KAAI,CAAC;cACjCC,KAAK,EAAEvE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuE,KAAK;cACtBC,cAAc,EAAE;YAClB,CAAE;YACF/D,OAAO,EAAEA,OAAQ;YACjBgE,UAAU,EAAEzE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D,IAAK;YAC3Bc,MAAM,EAAGC,GAAG,IAAKA,GAAG,CAACC,EAAE,IAAID,GAAG,CAACE,IAAI,IAAK,WAAUF,GAAG,CAACjE,KAAM;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAClE,EAAA,CApTID,cAAc;EAAA,QACJR,cAAc,EACXF,WAAW,EAcqBT,WAAW,EAIhCA,WAAW,EAIhBA,WAAW,EACjBD,WAAW,EA2H5BQ,YAAY,EAIZA,YAAY,EAIZA,YAAY;AAAA;AAAAiH,EAAA,GA5JRrG,cAAc;AAsTpB,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}