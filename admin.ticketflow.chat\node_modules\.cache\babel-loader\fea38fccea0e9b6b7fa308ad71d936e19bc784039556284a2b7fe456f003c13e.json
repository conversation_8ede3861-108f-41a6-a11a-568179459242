{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\chart-widget.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { Card } from 'antd';\nimport Apex<PERSON>hart from 'react-apexcharts';\nimport ReactResizeDetector from 'react-resize-detector';\nimport { apexAreaChartDefaultOption, apexBarChartDefaultOption, apexLineChartDefaultOption, apexPieChartDefaultOption } from '../constants/ChartConstant';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DIR_RTL = 'rtl';\nconst titleStyle = {\n  position: 'absolute',\n  zIndex: '1'\n};\nconst extraStyle = {\n  position: 'absolute',\n  zIndex: '1',\n  right: '0',\n  top: '-2px'\n};\nconst getChartTypeDefaultOption = type => {\n  switch (type) {\n    case 'line':\n      return apexLineChartDefaultOption;\n    case 'bar':\n      return apexBarChartDefaultOption;\n    case 'area':\n      return apexAreaChartDefaultOption;\n    case 'pie':\n      return apexPieChartDefaultOption;\n    default:\n      return apexLineChartDefaultOption;\n  }\n};\nconst ChartWidget = ({\n  title,\n  series,\n  width,\n  height,\n  xAxis,\n  customOptions,\n  card,\n  type,\n  extra,\n  direction,\n  bodyClass\n}) => {\n  _s();\n  let options = JSON.parse(JSON.stringify(getChartTypeDefaultOption(type)));\n\n  // Ensure series has valid data to prevent chart errors\n  const safeSeries = series && Array.isArray(series) ? series.map(item => ({\n    ...item,\n    data: item.data && Array.isArray(item.data) ? item.data : []\n  })) : [];\n  const isMobile = window.innerWidth < 768;\n  const setLegendOffset = () => {\n    if (chartRef.current) {\n      var _extraRef$current;\n      const lengend = chartRef.current.querySelectorAll('div.apexcharts-legend')[0];\n      lengend.style.marginRight = `${isMobile ? 0 : (_extraRef$current = extraRef.current) === null || _extraRef$current === void 0 ? void 0 : _extraRef$current.offsetWidth}px`;\n      if (direction === DIR_RTL) {\n        lengend.style.right = 'auto';\n        lengend.style.left = '0';\n      }\n      if (isMobile) {\n        lengend.style.position = 'relative';\n        lengend.style.top = 0;\n        lengend.style.justifyContent = 'start';\n        lengend.style.padding = 0;\n      }\n    }\n  };\n  useEffect(() => {\n    setLegendOffset();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const extraRef = useRef(null);\n  const chartRef = useRef();\n  options.xaxis = {\n    categories: xAxis && Array.isArray(xAxis) ? xAxis : []\n  };\n  if (customOptions) {\n    options = {\n      ...options,\n      ...customOptions\n    };\n  }\n  const onResize = () => {\n    setTimeout(() => {\n      setLegendOffset();\n    }, 600);\n  };\n  const renderChart = () => /*#__PURE__*/_jsxDEV(ReactResizeDetector, {\n    handleWidth: true,\n    handleHeight: true,\n    onResize: onResize,\n    targetRef: chartRef,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: direction === DIR_RTL ? {\n        direction: 'ltr'\n      } : {},\n      className: \"chartRef\",\n      ref: chartRef,\n      children: /*#__PURE__*/_jsxDEV(ApexChart, {\n        options: options,\n        type: type,\n        series: safeSeries,\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: card ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `position-relative ${bodyClass}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: !isMobile ? titleStyle : {},\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 14\n        }, this) && /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-weight-bold\",\n          style: !isMobile ? titleStyle : {},\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 15\n        }, this), extra && /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: extraRef,\n          style: !isMobile ? extraStyle : {},\n          children: extra\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 15\n        }, this), renderChart()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 9\n    }, this) : renderChart()\n  }, void 0, false);\n};\n_s(ChartWidget, \"yuOowm7QWeqbFUnrB2gsEuXomek=\");\n_c = ChartWidget;\nChartWidget.propTypes = {\n  title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),\n  series: PropTypes.array.isRequired,\n  xAxis: PropTypes.array,\n  customOptions: PropTypes.object,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  card: PropTypes.bool,\n  type: PropTypes.string,\n  extra: PropTypes.element,\n  bodyClass: PropTypes.string\n};\nChartWidget.defaultProps = {\n  series: [],\n  height: 300,\n  width: '100%',\n  card: true,\n  type: 'line'\n};\nexport default ChartWidget;\nvar _c;\n$RefreshReg$(_c, \"ChartWidget\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "PropTypes", "Card", "ApexChart", "ReactResizeDetector", "apexAreaChartDefaultOption", "apexBarChartDefaultOption", "apexLineChartDefaultOption", "apexPieChartDefaultOption", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DIR_RTL", "titleStyle", "position", "zIndex", "extraStyle", "right", "top", "getChartTypeDefaultOption", "type", "ChartWidget", "title", "series", "width", "height", "xAxis", "customOptions", "card", "extra", "direction", "bodyClass", "_s", "options", "JSON", "parse", "stringify", "safeSeries", "Array", "isArray", "map", "item", "data", "isMobile", "window", "innerWidth", "setLegendOffset", "chartRef", "current", "_extraRef$current", "lengend", "querySelectorAll", "style", "marginRight", "extraRef", "offsetWidth", "left", "justifyContent", "padding", "xaxis", "categories", "onResize", "setTimeout", "<PERSON><PERSON><PERSON>", "handleWidth", "handleHeight", "targetRef", "children", "className", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "propTypes", "oneOfType", "string", "element", "array", "isRequired", "object", "number", "bool", "defaultProps", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/chart-widget.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { Card } from 'antd';\nimport Apex<PERSON>hart from 'react-apexcharts';\nimport ReactResizeDetector from 'react-resize-detector';\nimport {\n  apexAreaChartDefaultOption,\n  apexBarChartDefaultOption,\n  apexLineChartDefaultOption,\n  apexPieChartDefaultOption,\n} from '../constants/ChartConstant';\n\nconst DIR_RTL = 'rtl';\n\nconst titleStyle = {\n  position: 'absolute',\n  zIndex: '1',\n};\n\nconst extraStyle = {\n  position: 'absolute',\n  zIndex: '1',\n  right: '0',\n  top: '-2px',\n};\n\nconst getChartTypeDefaultOption = (type) => {\n  switch (type) {\n    case 'line':\n      return apexLineChartDefaultOption;\n    case 'bar':\n      return apexBarChartDefaultOption;\n    case 'area':\n      return apexAreaChartDefaultOption;\n    case 'pie':\n      return apexPieChartDefaultOption;\n    default:\n      return apexLineChartDefaultOption;\n  }\n};\n\nconst ChartWidget = ({\n  title,\n  series,\n  width,\n  height,\n  xAxis,\n  customOptions,\n  card,\n  type,\n  extra,\n  direction,\n  bodyClass,\n}) => {\n  let options = JSON.parse(JSON.stringify(getChartTypeDefaultOption(type)));\n\n  // Ensure series has valid data to prevent chart errors\n  const safeSeries = series && Array.isArray(series)\n    ? series.map(item => ({\n        ...item,\n        data: item.data && Array.isArray(item.data) ? item.data : []\n      }))\n    : [];\n\n  const isMobile = window.innerWidth < 768;\n  const setLegendOffset = () => {\n    if (chartRef.current) {\n      const lengend = chartRef.current.querySelectorAll(\n        'div.apexcharts-legend'\n      )[0];\n      lengend.style.marginRight = `${\n        isMobile ? 0 : extraRef.current?.offsetWidth\n      }px`;\n      if (direction === DIR_RTL) {\n        lengend.style.right = 'auto';\n        lengend.style.left = '0';\n      }\n      if (isMobile) {\n        lengend.style.position = 'relative';\n        lengend.style.top = 0;\n        lengend.style.justifyContent = 'start';\n        lengend.style.padding = 0;\n      }\n    }\n  };\n\n  useEffect(() => {\n    setLegendOffset();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const extraRef = useRef(null);\n  const chartRef = useRef();\n\n  options.xaxis = {\n    categories: xAxis && Array.isArray(xAxis) ? xAxis : [],\n  };\n  if (customOptions) {\n    options = { ...options, ...customOptions };\n  }\n\n  const onResize = () => {\n    setTimeout(() => {\n      setLegendOffset();\n    }, 600);\n  };\n\n  const renderChart = () => (\n    <ReactResizeDetector\n      handleWidth\n      handleHeight\n      onResize={onResize}\n      targetRef={chartRef}\n    >\n      <div\n        style={direction === DIR_RTL ? { direction: 'ltr' } : {}}\n        className='chartRef'\n        ref={chartRef}\n      >\n        <ApexChart\n          options={options}\n          type={type}\n          series={safeSeries}\n          width={width}\n          height={height}\n        />\n      </div>\n    </ReactResizeDetector>\n  );\n\n  return (\n    <>\n      {card ? (\n        <Card>\n          <div className={`position-relative ${bodyClass}`}>\n            {<div style={!isMobile ? titleStyle : {}}>{title}</div> && (\n              <h4\n                className='font-weight-bold'\n                style={!isMobile ? titleStyle : {}}\n              >\n                {title}\n              </h4>\n            )}\n            {extra && (\n              <div ref={extraRef} style={!isMobile ? extraStyle : {}}>\n                {extra}\n              </div>\n            )}\n            {renderChart()}\n          </div>\n        </Card>\n      ) : (\n        renderChart()\n      )}\n    </>\n  );\n};\n\nChartWidget.propTypes = {\n  title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),\n  series: PropTypes.array.isRequired,\n  xAxis: PropTypes.array,\n  customOptions: PropTypes.object,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  card: PropTypes.bool,\n  type: PropTypes.string,\n  extra: PropTypes.element,\n  bodyClass: PropTypes.string,\n};\n\nChartWidget.defaultProps = {\n  series: [],\n  height: 300,\n  width: '100%',\n  card: true,\n  type: 'line',\n};\n\nexport default ChartWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,IAAI,QAAQ,MAAM;AAC3B,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SACEC,0BAA0B,EAC1BC,yBAAyB,EACzBC,0BAA0B,EAC1BC,yBAAyB,QACpB,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,OAAO,GAAG,KAAK;AAErB,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,UAAU,GAAG;EACjBF,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,GAAG;EACXE,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,yBAAyB,GAAIC,IAAI,IAAK;EAC1C,QAAQA,IAAI;IACV,KAAK,MAAM;MACT,OAAOd,0BAA0B;IACnC,KAAK,KAAK;MACR,OAAOD,yBAAyB;IAClC,KAAK,MAAM;MACT,OAAOD,0BAA0B;IACnC,KAAK,KAAK;MACR,OAAOG,yBAAyB;IAClC;MACE,OAAOD,0BAA0B;EACrC;AACF,CAAC;AAED,MAAMe,WAAW,GAAGA,CAAC;EACnBC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,aAAa;EACbC,IAAI;EACJR,IAAI;EACJS,KAAK;EACLC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACjB,yBAAyB,CAACC,IAAI,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAMiB,UAAU,GAAGd,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,GAC9CA,MAAM,CAACiB,GAAG,CAACC,IAAI,KAAK;IAClB,GAAGA,IAAI;IACPC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAIJ,KAAK,CAACC,OAAO,CAACE,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI,CAACC,IAAI,GAAG;EAC5D,CAAC,CAAC,CAAC,GACH,EAAE;EAEN,MAAMC,QAAQ,GAAGC,MAAM,CAACC,UAAU,GAAG,GAAG;EACxC,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,QAAQ,CAACC,OAAO,EAAE;MAAA,IAAAC,iBAAA;MACpB,MAAMC,OAAO,GAAGH,QAAQ,CAACC,OAAO,CAACG,gBAAgB,CAC/C,uBACF,CAAC,CAAC,CAAC,CAAC;MACJD,OAAO,CAACE,KAAK,CAACC,WAAW,GAAI,GAC3BV,QAAQ,GAAG,CAAC,IAAAM,iBAAA,GAAGK,QAAQ,CAACN,OAAO,cAAAC,iBAAA,uBAAhBA,iBAAA,CAAkBM,WAClC,IAAG;MACJ,IAAIzB,SAAS,KAAKlB,OAAO,EAAE;QACzBsC,OAAO,CAACE,KAAK,CAACnC,KAAK,GAAG,MAAM;QAC5BiC,OAAO,CAACE,KAAK,CAACI,IAAI,GAAG,GAAG;MAC1B;MACA,IAAIb,QAAQ,EAAE;QACZO,OAAO,CAACE,KAAK,CAACtC,QAAQ,GAAG,UAAU;QACnCoC,OAAO,CAACE,KAAK,CAAClC,GAAG,GAAG,CAAC;QACrBgC,OAAO,CAACE,KAAK,CAACK,cAAc,GAAG,OAAO;QACtCP,OAAO,CAACE,KAAK,CAACM,OAAO,GAAG,CAAC;MAC3B;IACF;EACF,CAAC;EAED3D,SAAS,CAAC,MAAM;IACd+C,eAAe,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,QAAQ,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMiD,QAAQ,GAAGjD,MAAM,CAAC,CAAC;EAEzBmC,OAAO,CAAC0B,KAAK,GAAG;IACdC,UAAU,EAAElC,KAAK,IAAIY,KAAK,CAACC,OAAO,CAACb,KAAK,CAAC,GAAGA,KAAK,GAAG;EACtD,CAAC;EACD,IAAIC,aAAa,EAAE;IACjBM,OAAO,GAAG;MAAE,GAAGA,OAAO;MAAE,GAAGN;IAAc,CAAC;EAC5C;EAEA,MAAMkC,QAAQ,GAAGA,CAAA,KAAM;IACrBC,UAAU,CAAC,MAAM;MACfhB,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,kBAClBtD,OAAA,CAACN,mBAAmB;IAClB6D,WAAW;IACXC,YAAY;IACZJ,QAAQ,EAAEA,QAAS;IACnBK,SAAS,EAAEnB,QAAS;IAAAoB,QAAA,eAEpB1D,OAAA;MACE2C,KAAK,EAAEtB,SAAS,KAAKlB,OAAO,GAAG;QAAEkB,SAAS,EAAE;MAAM,CAAC,GAAG,CAAC,CAAE;MACzDsC,SAAS,EAAC,UAAU;MACpBC,GAAG,EAAEtB,QAAS;MAAAoB,QAAA,eAEd1D,OAAA,CAACP,SAAS;QACR+B,OAAO,EAAEA,OAAQ;QACjBb,IAAI,EAAEA,IAAK;QACXG,MAAM,EAAEc,UAAW;QACnBb,KAAK,EAAEA,KAAM;QACbC,MAAM,EAAEA;MAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CACtB;EAED,oBACEhE,OAAA,CAAAE,SAAA;IAAAwD,QAAA,EACGvC,IAAI,gBACHnB,OAAA,CAACR,IAAI;MAAAkE,QAAA,eACH1D,OAAA;QAAK2D,SAAS,EAAG,qBAAoBrC,SAAU,EAAE;QAAAoC,QAAA,GAC9C,aAAA1D,OAAA;UAAK2C,KAAK,EAAE,CAACT,QAAQ,GAAG9B,UAAU,GAAG,CAAC,CAAE;UAAAsD,QAAA,EAAE7C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,iBACrDhE,OAAA;UACE2D,SAAS,EAAC,kBAAkB;UAC5BhB,KAAK,EAAE,CAACT,QAAQ,GAAG9B,UAAU,GAAG,CAAC,CAAE;UAAAsD,QAAA,EAElC7C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACL,EACA5C,KAAK,iBACJpB,OAAA;UAAK4D,GAAG,EAAEf,QAAS;UAACF,KAAK,EAAE,CAACT,QAAQ,GAAG3B,UAAU,GAAG,CAAC,CAAE;UAAAmD,QAAA,EACpDtC;QAAK;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EACAV,WAAW,CAAC,CAAC;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,GAEPV,WAAW,CAAC;EACb,gBACD,CAAC;AAEP,CAAC;AAAC/B,EAAA,CAnHIX,WAAW;AAAAqD,EAAA,GAAXrD,WAAW;AAqHjBA,WAAW,CAACsD,SAAS,GAAG;EACtBrD,KAAK,EAAEtB,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,MAAM,EAAE7E,SAAS,CAAC8E,OAAO,CAAC,CAAC;EACjEvD,MAAM,EAAEvB,SAAS,CAAC+E,KAAK,CAACC,UAAU;EAClCtD,KAAK,EAAE1B,SAAS,CAAC+E,KAAK;EACtBpD,aAAa,EAAE3B,SAAS,CAACiF,MAAM;EAC/BzD,KAAK,EAAExB,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,MAAM,EAAE7E,SAAS,CAACkF,MAAM,CAAC,CAAC;EAChEzD,MAAM,EAAEzB,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,MAAM,EAAE7E,SAAS,CAACkF,MAAM,CAAC,CAAC;EACjEtD,IAAI,EAAE5B,SAAS,CAACmF,IAAI;EACpB/D,IAAI,EAAEpB,SAAS,CAAC6E,MAAM;EACtBhD,KAAK,EAAE7B,SAAS,CAAC8E,OAAO;EACxB/C,SAAS,EAAE/B,SAAS,CAAC6E;AACvB,CAAC;AAEDxD,WAAW,CAAC+D,YAAY,GAAG;EACzB7D,MAAM,EAAE,EAAE;EACVE,MAAM,EAAE,GAAG;EACXD,KAAK,EAAE,MAAM;EACbI,IAAI,EAAE,IAAI;EACVR,IAAI,EAAE;AACR,CAAC;AAED,eAAeC,WAAW;AAAC,IAAAqD,EAAA;AAAAW,YAAA,CAAAX,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}