{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\drawing-map.js\",\n  _s = $RefreshSig$();\nimport { Map, Marker, Polygon, Polyline } from 'google-maps-react';\nimport React from 'react';\nimport { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport getDefaultCenter from 'helpers/getDefaultCenter';\nimport { withGoogleMaps } from './GoogleMapsWrapper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst mapApiKey = getMapApiKey();\nconst defaultCenter = getDefaultCenter();\nconst DrawingManager = props => {\n  _s();\n  // Memoize the raw coordinates to prevent infinite re-renders\n  const rawCoords = useMemo(() => {\n    return Array.isArray(props.triangleCoords) ? props.triangleCoords : [];\n  }, [props.triangleCoords]);\n\n  // Memoize the coordinate transformation to prevent infinite re-renders\n  const validTriangleCoords = useMemo(() => {\n    if (!Array.isArray(rawCoords)) {\n      return [];\n    }\n    return rawCoords.map(coord => {\n      if (coord && typeof coord === 'object' && coord.lat !== undefined && coord.lng !== undefined) {\n        const lat = Number(coord.lat);\n        const lng = Number(coord.lng);\n\n        // Ensure coordinates are valid numbers\n        if (!isNaN(lat) && !isNaN(lng) && isFinite(lat) && isFinite(lng)) {\n          return {\n            lat,\n            lng\n          };\n        }\n      }\n      return null;\n    }).filter(coord => coord !== null);\n  }, [rawCoords]);\n  const [markers, setMarkers] = useState(validTriangleCoords);\n  const [center, setCenter] = useState(defaultCenter);\n  const [polygon, setPolygon] = useState(validTriangleCoords);\n  const [finish, setFinish] = useState(validTriangleCoords.length > 0);\n  const [focus, setFocus] = useState(false);\n  const mapRef = useRef(null);\n  const isMountedRef = useRef(true);\n\n  // Track component mounting state\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  // Update markers when triangleCoords prop changes\n  useEffect(() => {\n    if (isMountedRef.current) {\n      setMarkers(validTriangleCoords);\n    }\n  }, [validTriangleCoords]);\n  useEffect(() => {\n    if (isMountedRef.current && props.setMerge) {\n      props.setMerge(finish);\n    }\n  }, [finish, props.setMerge]);\n  const onClick = (t, map, cord) => {\n    if (!isMountedRef.current) return;\n    setFocus(false);\n    const {\n      latLng\n    } = cord;\n    const lat = latLng.lat();\n    const lng = latLng.lng();\n    if (finish) {\n      setPolygon([]);\n      props.settriangleCoords([{\n        lat,\n        lng\n      }]);\n      setCenter({\n        lat,\n        lng\n      });\n      setFinish(false);\n    } else {\n      props.settriangleCoords(prev => [...prev, {\n        lat,\n        lng\n      }]);\n    }\n  };\n  const onFinish = e => {\n    var _validTriangleCoords$, _e$position;\n    if (!isMountedRef.current) return;\n    setFinish(validTriangleCoords.length > 0);\n    if (((_validTriangleCoords$ = validTriangleCoords[0]) === null || _validTriangleCoords$ === void 0 ? void 0 : _validTriangleCoords$.lat) === ((_e$position = e.position) === null || _e$position === void 0 ? void 0 : _e$position.lat) && validTriangleCoords.length > 1) {\n      setPolygon(validTriangleCoords);\n      props.setLocation && props.setLocation(validTriangleCoords);\n      setFinish(true);\n      setFocus(true);\n    }\n  };\n  const currentLocation = () => {\n    if (!isMountedRef.current) return;\n    navigator.geolocation.getCurrentPosition(function (position) {\n      if (isMountedRef.current) {\n        setCenter({\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        });\n      }\n    }, function (error) {\n      console.error('Error getting current location:', error);\n    });\n  };\n  useEffect(() => {\n    setFocus(true);\n  }, []);\n  function handleMapReady(_, map) {\n    if (!isMountedRef.current || !map) return;\n    mapRef.current = map;\n    map.setOptions({\n      draggableCursor: 'crosshair',\n      draggingCursor: 'grab'\n    });\n  }\n  let bounds = null;\n  if (props.google && props.google.maps) {\n    bounds = new props.google.maps.LatLngBounds();\n    if (markers.length > 0) {\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n    } else {\n      bounds.extend(center);\n    }\n  }\n  const OPTIONS = {\n    minZoom: 15,\n    maxZoom: 15\n  };\n\n  // Show loading if Google Maps is not ready\n  if (!props.google || !props.google.maps) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"map-container\",\n      style: {\n        height: 500,\n        width: '100%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading Google Maps...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"map-container\",\n    style: {\n      height: 500,\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"map-button\",\n      type: \"button\",\n      onClick: () => {\n        currentLocation();\n      },\n      children: /*#__PURE__*/_jsxDEV(BiCurrentLocation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Map, {\n      options: OPTIONS,\n      cursor: \"pointer\",\n      onClick: onClick,\n      maxZoom: 16,\n      minZoom: 2,\n      google: props.google,\n      initialCenter: defaultCenter,\n      center: center,\n      onReady: handleMapReady,\n      bounds: focus && bounds && markers.length > 0 ? bounds : undefined,\n      className: \"clickable\",\n      children: [Array.isArray(validTriangleCoords) && validTriangleCoords.length > 0 && validTriangleCoords.map((item, idx) => {\n        var _props$google;\n        // Additional validation for each marker position\n        if (!item || typeof item !== 'object' || isNaN(item.lat) || isNaN(item.lng)) {\n          return null;\n        }\n        return /*#__PURE__*/_jsxDEV(Marker, {\n          onClick: e => onFinish(e),\n          position: item,\n          icon: {\n            url: 'https://upload.wikimedia.org/wikipedia/commons/9/94/Circle-image.svg',\n            scaledSize: (_props$google = props.google) !== null && _props$google !== void 0 && _props$google.maps ? new props.google.maps.Size(10, 10) : undefined\n          },\n          className: \"marker\"\n        }, idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this);\n      }), validTriangleCoords.length > 0 && (!(polygon !== null && polygon !== void 0 && polygon.length) ? /*#__PURE__*/_jsxDEV(Polyline, {\n        path: validTriangleCoords,\n        strokeColor: \"black\",\n        strokeOpacity: 0.8,\n        strokeWeight: 3,\n        fillColor: \"black\",\n        fillOpacity: 0.35\n      }, validTriangleCoords.length, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Polygon, {\n        path: validTriangleCoords,\n        strokeColor: \"black\",\n        strokeOpacity: 0.8,\n        strokeWeight: 3,\n        fillColor: \"black\",\n        fillOpacity: 0.35\n      }, polygon.length, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(DrawingManager, \"BfniUxSQeY+2FD8wUnR+UxnANmc=\");\n_c = DrawingManager;\nexport default _c2 = withGoogleMaps(DrawingManager, {\n  LoadingContainer: () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading Google Maps...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 27\n  }, this),\n  ErrorContainer: ({\n    error\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center',\n      color: 'red'\n    },\n    children: [\"Error loading Google Maps: \", (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error']\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this)\n});\nvar _c, _c2;\n$RefreshReg$(_c, \"DrawingManager\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["Map", "<PERSON><PERSON>", "Polygon", "Polyline", "React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "BiCurrentLocation", "getMapApiKey", "getDefaultCenter", "withGoogleMaps", "jsxDEV", "_jsxDEV", "mapApiKey", "defaultCenter", "DrawingManager", "props", "_s", "rawCoords", "Array", "isArray", "triangleCoords", "validTriangleCoords", "map", "coord", "lat", "undefined", "lng", "Number", "isNaN", "isFinite", "filter", "markers", "setMarkers", "center", "setCenter", "polygon", "setPolygon", "finish", "<PERSON><PERSON><PERSON><PERSON>", "length", "focus", "setFocus", "mapRef", "isMountedRef", "current", "setMerge", "onClick", "t", "cord", "latLng", "settriangleCoords", "prev", "onFinish", "e", "_validTriangleCoords$", "_e$position", "position", "setLocation", "currentLocation", "navigator", "geolocation", "getCurrentPosition", "coords", "latitude", "longitude", "error", "console", "handleMapReady", "_", "setOptions", "draggableCursor", "draggingCursor", "bounds", "google", "maps", "LatLngBounds", "i", "extend", "OPTIONS", "minZoom", "max<PERSON><PERSON>", "className", "style", "height", "width", "display", "alignItems", "justifyContent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "options", "cursor", "initialCenter", "onReady", "item", "idx", "_props$google", "icon", "url", "scaledSize", "Size", "path", "strokeColor", "strokeOpacity", "strokeWeight", "fillColor", "fillOpacity", "_c", "_c2", "LoadingContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "padding", "textAlign", "color", "message", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/drawing-map.js"], "sourcesContent": ["import {\n  Map,\n  Marker,\n  Polygon,\n  Polyline,\n} from 'google-maps-react';\nimport React from 'react';\nimport { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport getDefaultCenter from 'helpers/getDefaultCenter';\nimport { withGoogleMaps } from './GoogleMapsWrapper';\n\nconst mapApiKey = getMapApiKey();\nconst defaultCenter = getDefaultCenter();\n\nconst DrawingManager = (props) => {\n  // Memoize the raw coordinates to prevent infinite re-renders\n  const rawCoords = useMemo(() => {\n    return Array.isArray(props.triangleCoords) ? props.triangleCoords : [];\n  }, [props.triangleCoords]);\n\n  // Memoize the coordinate transformation to prevent infinite re-renders\n  const validTriangleCoords = useMemo(() => {\n    if (!Array.isArray(rawCoords)) {\n      return [];\n    }\n\n    return rawCoords.map(coord => {\n      if (coord && typeof coord === 'object' && coord.lat !== undefined && coord.lng !== undefined) {\n        const lat = Number(coord.lat);\n        const lng = Number(coord.lng);\n\n        // Ensure coordinates are valid numbers\n        if (!isNaN(lat) && !isNaN(lng) && isFinite(lat) && isFinite(lng)) {\n          return { lat, lng };\n        }\n      }\n      return null;\n    }).filter(coord => coord !== null);\n  }, [rawCoords]);\n\n\n\n  const [markers, setMarkers] = useState(validTriangleCoords);\n  const [center, setCenter] = useState(defaultCenter);\n  const [polygon, setPolygon] = useState(validTriangleCoords);\n  const [finish, setFinish] = useState(validTriangleCoords.length > 0);\n  const [focus, setFocus] = useState(false);\n  const mapRef = useRef(null);\n  const isMountedRef = useRef(true);\n\n\n\n  // Track component mounting state\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  // Update markers when triangleCoords prop changes\n  useEffect(() => {\n    if (isMountedRef.current) {\n      setMarkers(validTriangleCoords);\n    }\n  }, [validTriangleCoords]);\n\n  useEffect(() => {\n    if (isMountedRef.current && props.setMerge) {\n      props.setMerge(finish);\n    }\n  }, [finish, props.setMerge]);\n\n  const onClick = (t, map, cord) => {\n    if (!isMountedRef.current) return;\n\n    setFocus(false);\n    const { latLng } = cord;\n    const lat = latLng.lat();\n    const lng = latLng.lng();\n    if (finish) {\n      setPolygon([]);\n      props.settriangleCoords([{ lat, lng }]);\n      setCenter({ lat, lng });\n      setFinish(false);\n    } else {\n      props.settriangleCoords((prev) => [...prev, { lat, lng }]);\n    }\n  };\n\n  const onFinish = (e) => {\n    if (!isMountedRef.current) return;\n\n    setFinish(validTriangleCoords.length > 0);\n    if (\n      validTriangleCoords[0]?.lat === e.position?.lat &&\n      validTriangleCoords.length > 1\n    ) {\n      setPolygon(validTriangleCoords);\n      props.setLocation && props.setLocation(validTriangleCoords);\n      setFinish(true);\n      setFocus(true);\n    }\n  };\n\n  const currentLocation = () => {\n    if (!isMountedRef.current) return;\n\n    navigator.geolocation.getCurrentPosition(\n      function (position) {\n        if (isMountedRef.current) {\n          setCenter({\n            lat: position.coords.latitude,\n            lng: position.coords.longitude,\n          });\n        }\n      },\n      function (error) {\n        console.error('Error getting current location:', error);\n      }\n    );\n  };\n\n  useEffect(() => {\n    setFocus(true);\n  }, []);\n\n  function handleMapReady(_, map) {\n    if (!isMountedRef.current || !map) return;\n\n    mapRef.current = map;\n    map.setOptions({\n      draggableCursor: 'crosshair',\n      draggingCursor: 'grab',\n    });\n  }\n\n\n\n  let bounds = null;\n  if (props.google && props.google.maps) {\n    bounds = new props.google.maps.LatLngBounds();\n    if (markers.length > 0) {\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n    } else {\n      bounds.extend(center);\n    }\n  }\n\n  const OPTIONS = {\n    minZoom: 15,\n    maxZoom: 15,\n  };\n\n  // Show loading if Google Maps is not ready\n  if (!props.google || !props.google.maps) {\n    return (\n      <div className='map-container' style={{ height: 500, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <div>Loading Google Maps...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className='map-container' style={{ height: 500, width: '100%' }}>\n      <button\n        className='map-button'\n        type='button'\n        onClick={() => {\n          currentLocation();\n        }}\n      >\n        <BiCurrentLocation />\n      </button>\n      <Map\n        options={OPTIONS}\n        cursor='pointer'\n        onClick={onClick}\n        maxZoom={16}\n        minZoom={2}\n        google={props.google}\n        initialCenter={defaultCenter}\n        center={center}\n        onReady={handleMapReady}\n        bounds={focus && bounds && markers.length > 0 ? bounds : undefined}\n        className='clickable'\n      >\n        {Array.isArray(validTriangleCoords) && validTriangleCoords.length > 0 && validTriangleCoords.map((item, idx) => {\n          // Additional validation for each marker position\n          if (!item || typeof item !== 'object' || isNaN(item.lat) || isNaN(item.lng)) {\n            return null;\n          }\n\n          return (\n            <Marker\n              onClick={(e) => onFinish(e)}\n              key={idx}\n              position={item}\n              icon={{\n                url: 'https://upload.wikimedia.org/wikipedia/commons/9/94/Circle-image.svg',\n                scaledSize: props.google?.maps ? new props.google.maps.Size(10, 10) : undefined,\n              }}\n              className='marker'\n            />\n          );\n        })}\n\n        {validTriangleCoords.length > 0 && (\n          !polygon?.length ? (\n            <Polyline\n              key={validTriangleCoords.length}\n              path={validTriangleCoords}\n              strokeColor='black'\n              strokeOpacity={0.8}\n              strokeWeight={3}\n              fillColor='black'\n              fillOpacity={0.35}\n\n            />\n          ) : (\n            <Polygon\n              key={polygon.length}\n              path={validTriangleCoords}\n              strokeColor='black'\n              strokeOpacity={0.8}\n              strokeWeight={3}\n              fillColor='black'\n              fillOpacity={0.35}\n\n            />\n          )\n        )}\n      </Map>\n    </div>\n  );\n};\n\nexport default withGoogleMaps(DrawingManager, {\n  LoadingContainer: () => <div>Loading Google Maps...</div>,\n  ErrorContainer: ({ error }) => (\n    <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>\n      Error loading Google Maps: {error?.message || 'Unknown error'}\n    </div>\n  ),\n});\n"], "mappings": ";;AAAA,SACEA,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,QAAQ,QACH,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACzE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,SAASC,cAAc,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGL,YAAY,CAAC,CAAC;AAChC,MAAMM,aAAa,GAAGL,gBAAgB,CAAC,CAAC;AAExC,MAAMM,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAChC;EACA,MAAMC,SAAS,GAAGZ,OAAO,CAAC,MAAM;IAC9B,OAAOa,KAAK,CAACC,OAAO,CAACJ,KAAK,CAACK,cAAc,CAAC,GAAGL,KAAK,CAACK,cAAc,GAAG,EAAE;EACxE,CAAC,EAAE,CAACL,KAAK,CAACK,cAAc,CAAC,CAAC;;EAE1B;EACA,MAAMC,mBAAmB,GAAGhB,OAAO,CAAC,MAAM;IACxC,IAAI,CAACa,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC7B,OAAO,EAAE;IACX;IAEA,OAAOA,SAAS,CAACK,GAAG,CAACC,KAAK,IAAI;MAC5B,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,GAAG,KAAKC,SAAS,IAAIF,KAAK,CAACG,GAAG,KAAKD,SAAS,EAAE;QAC5F,MAAMD,GAAG,GAAGG,MAAM,CAACJ,KAAK,CAACC,GAAG,CAAC;QAC7B,MAAME,GAAG,GAAGC,MAAM,CAACJ,KAAK,CAACG,GAAG,CAAC;;QAE7B;QACA,IAAI,CAACE,KAAK,CAACJ,GAAG,CAAC,IAAI,CAACI,KAAK,CAACF,GAAG,CAAC,IAAIG,QAAQ,CAACL,GAAG,CAAC,IAAIK,QAAQ,CAACH,GAAG,CAAC,EAAE;UAChE,OAAO;YAAEF,GAAG;YAAEE;UAAI,CAAC;QACrB;MACF;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACI,MAAM,CAACP,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAC;EACpC,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EAIf,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAACoB,mBAAmB,CAAC;EAC3D,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAACY,aAAa,CAAC;EACnD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAACoB,mBAAmB,CAAC;EAC3D,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAACoB,mBAAmB,CAACkB,MAAM,GAAG,CAAC,CAAC;EACpE,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAMyC,MAAM,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMwC,YAAY,GAAGxC,MAAM,CAAC,IAAI,CAAC;;EAIjC;EACAD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXyC,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIyC,YAAY,CAACC,OAAO,EAAE;MACxBZ,UAAU,CAACX,mBAAmB,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzBnB,SAAS,CAAC,MAAM;IACd,IAAIyC,YAAY,CAACC,OAAO,IAAI7B,KAAK,CAAC8B,QAAQ,EAAE;MAC1C9B,KAAK,CAAC8B,QAAQ,CAACR,MAAM,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,MAAM,EAAEtB,KAAK,CAAC8B,QAAQ,CAAC,CAAC;EAE5B,MAAMC,OAAO,GAAGA,CAACC,CAAC,EAAEzB,GAAG,EAAE0B,IAAI,KAAK;IAChC,IAAI,CAACL,YAAY,CAACC,OAAO,EAAE;IAE3BH,QAAQ,CAAC,KAAK,CAAC;IACf,MAAM;MAAEQ;IAAO,CAAC,GAAGD,IAAI;IACvB,MAAMxB,GAAG,GAAGyB,MAAM,CAACzB,GAAG,CAAC,CAAC;IACxB,MAAME,GAAG,GAAGuB,MAAM,CAACvB,GAAG,CAAC,CAAC;IACxB,IAAIW,MAAM,EAAE;MACVD,UAAU,CAAC,EAAE,CAAC;MACdrB,KAAK,CAACmC,iBAAiB,CAAC,CAAC;QAAE1B,GAAG;QAAEE;MAAI,CAAC,CAAC,CAAC;MACvCQ,SAAS,CAAC;QAAEV,GAAG;QAAEE;MAAI,CAAC,CAAC;MACvBY,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,MAAM;MACLvB,KAAK,CAACmC,iBAAiB,CAAEC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE;QAAE3B,GAAG;QAAEE;MAAI,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAM0B,QAAQ,GAAIC,CAAC,IAAK;IAAA,IAAAC,qBAAA,EAAAC,WAAA;IACtB,IAAI,CAACZ,YAAY,CAACC,OAAO,EAAE;IAE3BN,SAAS,CAACjB,mBAAmB,CAACkB,MAAM,GAAG,CAAC,CAAC;IACzC,IACE,EAAAe,qBAAA,GAAAjC,mBAAmB,CAAC,CAAC,CAAC,cAAAiC,qBAAA,uBAAtBA,qBAAA,CAAwB9B,GAAG,QAAA+B,WAAA,GAAKF,CAAC,CAACG,QAAQ,cAAAD,WAAA,uBAAVA,WAAA,CAAY/B,GAAG,KAC/CH,mBAAmB,CAACkB,MAAM,GAAG,CAAC,EAC9B;MACAH,UAAU,CAACf,mBAAmB,CAAC;MAC/BN,KAAK,CAAC0C,WAAW,IAAI1C,KAAK,CAAC0C,WAAW,CAACpC,mBAAmB,CAAC;MAC3DiB,SAAS,CAAC,IAAI,CAAC;MACfG,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACf,YAAY,CAACC,OAAO,EAAE;IAE3Be,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACtC,UAAUL,QAAQ,EAAE;MAClB,IAAIb,YAAY,CAACC,OAAO,EAAE;QACxBV,SAAS,CAAC;UACRV,GAAG,EAAEgC,QAAQ,CAACM,MAAM,CAACC,QAAQ;UAC7BrC,GAAG,EAAE8B,QAAQ,CAACM,MAAM,CAACE;QACvB,CAAC,CAAC;MACJ;IACF,CAAC,EACD,UAAUC,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CACF,CAAC;EACH,CAAC;EAED/D,SAAS,CAAC,MAAM;IACduC,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,SAAS0B,cAAcA,CAACC,CAAC,EAAE9C,GAAG,EAAE;IAC9B,IAAI,CAACqB,YAAY,CAACC,OAAO,IAAI,CAACtB,GAAG,EAAE;IAEnCoB,MAAM,CAACE,OAAO,GAAGtB,GAAG;IACpBA,GAAG,CAAC+C,UAAU,CAAC;MACbC,eAAe,EAAE,WAAW;MAC5BC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;EAIA,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIzD,KAAK,CAAC0D,MAAM,IAAI1D,KAAK,CAAC0D,MAAM,CAACC,IAAI,EAAE;IACrCF,MAAM,GAAG,IAAIzD,KAAK,CAAC0D,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAAC;IAC7C,IAAI5C,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;MACtB,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7C,OAAO,CAACQ,MAAM,EAAEqC,CAAC,EAAE,EAAE;QACvCJ,MAAM,CAACK,MAAM,CAAC9C,OAAO,CAAC6C,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC,MAAM;MACLJ,MAAM,CAACK,MAAM,CAAC5C,MAAM,CAAC;IACvB;EACF;EAEA,MAAM6C,OAAO,GAAG;IACdC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC;;EAED;EACA,IAAI,CAACjE,KAAK,CAAC0D,MAAM,IAAI,CAAC1D,KAAK,CAAC0D,MAAM,CAACC,IAAI,EAAE;IACvC,oBACE/D,OAAA;MAAKsE,SAAS,EAAC,eAAe;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,eACpI7E,OAAA;QAAA6E,QAAA,EAAK;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEV;EAEA,oBACEjF,OAAA;IAAKsE,SAAS,EAAC,eAAe;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAI,QAAA,gBACnE7E,OAAA;MACEsE,SAAS,EAAC,YAAY;MACtBY,IAAI,EAAC,QAAQ;MACb/C,OAAO,EAAEA,CAAA,KAAM;QACbY,eAAe,CAAC,CAAC;MACnB,CAAE;MAAA8B,QAAA,eAEF7E,OAAA,CAACL,iBAAiB;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACTjF,OAAA,CAACf,GAAG;MACFkG,OAAO,EAAEhB,OAAQ;MACjBiB,MAAM,EAAC,SAAS;MAChBjD,OAAO,EAAEA,OAAQ;MACjBkC,OAAO,EAAE,EAAG;MACZD,OAAO,EAAE,CAAE;MACXN,MAAM,EAAE1D,KAAK,CAAC0D,MAAO;MACrBuB,aAAa,EAAEnF,aAAc;MAC7BoB,MAAM,EAAEA,MAAO;MACfgE,OAAO,EAAE9B,cAAe;MACxBK,MAAM,EAAEhC,KAAK,IAAIgC,MAAM,IAAIzC,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGiC,MAAM,GAAG/C,SAAU;MACnEwD,SAAS,EAAC,WAAW;MAAAO,QAAA,GAEpBtE,KAAK,CAACC,OAAO,CAACE,mBAAmB,CAAC,IAAIA,mBAAmB,CAACkB,MAAM,GAAG,CAAC,IAAIlB,mBAAmB,CAACC,GAAG,CAAC,CAAC4E,IAAI,EAAEC,GAAG,KAAK;QAAA,IAAAC,aAAA;QAC9G;QACA,IAAI,CAACF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAItE,KAAK,CAACsE,IAAI,CAAC1E,GAAG,CAAC,IAAII,KAAK,CAACsE,IAAI,CAACxE,GAAG,CAAC,EAAE;UAC3E,OAAO,IAAI;QACb;QAEA,oBACEf,OAAA,CAACd,MAAM;UACLiD,OAAO,EAAGO,CAAC,IAAKD,QAAQ,CAACC,CAAC,CAAE;UAE5BG,QAAQ,EAAE0C,IAAK;UACfG,IAAI,EAAE;YACJC,GAAG,EAAE,sEAAsE;YAC3EC,UAAU,EAAE,CAAAH,aAAA,GAAArF,KAAK,CAAC0D,MAAM,cAAA2B,aAAA,eAAZA,aAAA,CAAc1B,IAAI,GAAG,IAAI3D,KAAK,CAAC0D,MAAM,CAACC,IAAI,CAAC8B,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG/E;UACxE,CAAE;UACFwD,SAAS,EAAC;QAAQ,GANbkB,GAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOT,CAAC;MAEN,CAAC,CAAC,EAEDvE,mBAAmB,CAACkB,MAAM,GAAG,CAAC,KAC7B,EAACJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,MAAM,iBACd5B,OAAA,CAACZ,QAAQ;QAEP0G,IAAI,EAAEpF,mBAAoB;QAC1BqF,WAAW,EAAC,OAAO;QACnBC,aAAa,EAAE,GAAI;QACnBC,YAAY,EAAE,CAAE;QAChBC,SAAS,EAAC,OAAO;QACjBC,WAAW,EAAE;MAAK,GANbzF,mBAAmB,CAACkB,MAAM;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQhC,CAAC,gBAEFjF,OAAA,CAACb,OAAO;QAEN2G,IAAI,EAAEpF,mBAAoB;QAC1BqF,WAAW,EAAC,OAAO;QACnBC,aAAa,EAAE,GAAI;QACnBC,YAAY,EAAE,CAAE;QAChBC,SAAS,EAAC,OAAO;QACjBC,WAAW,EAAE;MAAK,GANb3E,OAAO,CAACI,MAAM;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQpB,CACF,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA9NIF,cAAc;AAAAiG,EAAA,GAAdjG,cAAc;AAgOpB,eAAAkG,GAAA,GAAevG,cAAc,CAACK,cAAc,EAAE;EAC5CmG,gBAAgB,EAAEA,CAAA,kBAAMtG,OAAA;IAAA6E,QAAA,EAAK;EAAsB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzDsB,cAAc,EAAEA,CAAC;IAAEjD;EAAM,CAAC,kBACxBtD,OAAA;IAAKuE,KAAK,EAAE;MAAEiC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAM,CAAE;IAAA7B,QAAA,GAAC,6BACvC,EAAC,CAAAvB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqD,OAAO,KAAI,eAAe;EAAA;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D;AAET,CAAC,CAAC;AAAC,IAAAmB,EAAA,EAAAC,GAAA;AAAAO,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}