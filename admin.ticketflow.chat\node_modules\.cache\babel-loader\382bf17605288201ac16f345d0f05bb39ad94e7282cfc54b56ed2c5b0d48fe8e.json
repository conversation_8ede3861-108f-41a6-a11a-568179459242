{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\order-board-skeleton.js\";\nimport React, { memo } from 'react';\nimport { Card, Skeleton, Space } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderBoardSkeleton = /*#__PURE__*/memo(_c = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-board-skeleton\",\n    children: [1, 2, 3, 4].map(column => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dnd-column\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        style: {\n          marginBottom: 16,\n          minHeight: 400\n        },\n        title: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n          style: {\n            width: 120\n          },\n          active: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 20\n        }, this),\n        children: [1, 2, 3].map(item => /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          style: {\n            marginBottom: 8\n          },\n          bodyStyle: {\n            padding: 12\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Skeleton.Input, {\n              style: {\n                width: 80\n              },\n              active: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n              active: true,\n              paragraph: {\n                rows: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Skeleton.Avatar, {\n                size: \"small\",\n                active: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n                style: {\n                  width: 100\n                },\n                active: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 17\n          }, this)\n        }, item, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 11\n      }, this)\n    }, column, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n});\n_c2 = OrderBoardSkeleton;\nexport default OrderBoardSkeleton;\nvar _c, _c2;\n$RefreshReg$(_c, \"OrderBoardSkeleton$memo\");\n$RefreshReg$(_c2, \"OrderBoardSkeleton\");", "map": {"version": 3, "names": ["React", "memo", "Card", "Skeleton", "Space", "jsxDEV", "_jsxDEV", "OrderBoardSkeleton", "_c", "className", "children", "map", "column", "size", "style", "marginBottom", "minHeight", "title", "Input", "width", "active", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "item", "bodyStyle", "padding", "direction", "paragraph", "rows", "Avatar", "_c2", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/order-board-skeleton.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { Card, Skeleton, Space } from 'antd';\n\nconst OrderBoardSkeleton = memo(() => {\n  return (\n    <div className=\"order-board-skeleton\">\n      {[1, 2, 3, 4].map((column) => (\n        <div key={column} className=\"dnd-column\">\n          <Card\n            size=\"small\"\n            style={{ marginBottom: 16, minHeight: 400 }}\n            title={<Skeleton.Input style={{ width: 120 }} active />}\n          >\n            {[1, 2, 3].map((item) => (\n              <Card\n                key={item}\n                size=\"small\"\n                style={{ marginBottom: 8 }}\n                bodyStyle={{ padding: 12 }}\n              >\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Skeleton.Input style={{ width: 80 }} active />\n                  <Skeleton active paragraph={{ rows: 2 }} />\n                  <Space>\n                    <Skeleton.Avatar size=\"small\" active />\n                    <Skeleton.Input style={{ width: 100 }} active />\n                  </Space>\n                </Space>\n              </Card>\n            ))}\n          </Card>\n        </div>\n      ))}\n    </div>\n  );\n});\n\nexport default OrderBoardSkeleton;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,kBAAkB,gBAAGN,IAAI,CAAAO,EAAA,GAACA,CAAA,KAAM;EACpC,oBACEF,OAAA;IAAKG,SAAS,EAAC,sBAAsB;IAAAC,QAAA,EAClC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,MAAM,iBACvBN,OAAA;MAAkBG,SAAS,EAAC,YAAY;MAAAC,QAAA,eACtCJ,OAAA,CAACJ,IAAI;QACHW,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE;UAAEC,YAAY,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE;QAC5CC,KAAK,eAAEX,OAAA,CAACH,QAAQ,CAACe,KAAK;UAACJ,KAAK,EAAE;YAAEK,KAAK,EAAE;UAAI,CAAE;UAACC,MAAM;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAd,QAAA,EAEvD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEc,IAAI,iBAClBnB,OAAA,CAACJ,IAAI;UAEHW,IAAI,EAAC,OAAO;UACZC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAE,CAAE;UAC3BW,SAAS,EAAE;YAAEC,OAAO,EAAE;UAAG,CAAE;UAAAjB,QAAA,eAE3BJ,OAAA,CAACF,KAAK;YAACwB,SAAS,EAAC,UAAU;YAACd,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,gBACnDJ,OAAA,CAACH,QAAQ,CAACe,KAAK;cAACJ,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAG,CAAE;cAACC,MAAM;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/ClB,OAAA,CAACH,QAAQ;cAACiB,MAAM;cAACS,SAAS,EAAE;gBAAEC,IAAI,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3ClB,OAAA,CAACF,KAAK;cAAAM,QAAA,gBACJJ,OAAA,CAACH,QAAQ,CAAC4B,MAAM;gBAAClB,IAAI,EAAC,OAAO;gBAACO,MAAM;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvClB,OAAA,CAACH,QAAQ,CAACe,KAAK;gBAACJ,KAAK,EAAE;kBAAEK,KAAK,EAAE;gBAAI,CAAE;gBAACC,MAAM;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAZHC,IAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaL,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC,GAvBCZ,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBX,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC,CAAC;AAACQ,GAAA,GAhCGzB,kBAAkB;AAkCxB,eAAeA,kBAAkB;AAAC,IAAAC,EAAA,EAAAwB,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}