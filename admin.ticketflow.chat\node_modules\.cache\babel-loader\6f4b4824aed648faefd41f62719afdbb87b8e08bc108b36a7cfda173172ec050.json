{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\privacy\\\\policy.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { Button, Card, Col, Form, Input, Row, Space } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { disableRefetch, setMenuData } from 'redux/slices/menu';\nimport privacyService from 'services/privacy';\nimport { useTranslation } from 'react-i18next';\nimport Loading from 'components/loading';\nimport LanguageList from 'components/language-list';\nimport getTranslationFields from 'helpers/getTranslationFields';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Policy() {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const {\n    languages,\n    defaultLang\n  } = useSelector(state => state.formLang, shallowEqual);\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({\n        activeMenu,\n        data\n      }));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  function getLanguageFields(data) {\n    if (!data) {\n      return {};\n    }\n    const {\n      translations\n    } = data;\n    const result = languages.map(item => {\n      var _translations$find, _translations$find2;\n      return {\n        [`title[${item.locale}]`]: (_translations$find = translations.find(el => el.locale === item.locale)) === null || _translations$find === void 0 ? void 0 : _translations$find.title,\n        [`description[${item.locale}]`]: (_translations$find2 = translations.find(el => el.locale === item.locale)) === null || _translations$find2 === void 0 ? void 0 : _translations$find2.description\n      };\n    });\n    return Object.assign({}, ...result);\n  }\n  function fetchPolicy() {\n    setLoading(true);\n    privacyService.getPolicy().then(({\n      data\n    }) => form.setFieldsValue({\n      ...getLanguageFields(data)\n    })).catch(() => {\n      toast.dismiss(404);\n    }).finally(() => {\n      setLoading(false);\n      dispatch(disableRefetch(activeMenu));\n    });\n  }\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchPolicy();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [activeMenu.refetch]);\n  const onFinish = values => {\n    const body = {\n      title: getTranslationFields(languages, values),\n      description: getTranslationFields(languages, values, 'description')\n    };\n    setLoadingBtn(true);\n    privacyService.createPolicy(body).then(() => {\n      toast.success(t('successfully.saved'));\n    }).finally(() => setLoadingBtn(false));\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: t('policy'),\n    extra: /*#__PURE__*/_jsxDEV(LanguageList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 38\n    }, this),\n    children: !loading ? /*#__PURE__*/_jsxDEV(Form, {\n      name: \"policy-form\",\n      layout: \"vertical\",\n      onFinish: onFinish,\n      form: form,\n      initialValues: activeMenu.data,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('title'),\n            name: `title[${item.locale}]`,\n            rules: [{\n              required: (item === null || item === void 0 ? void 0 : item.locale) === defaultLang,\n              message: t('required')\n            }],\n            hidden: item.locale !== defaultLang,\n            children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 19\n            }, this)\n          }, 'title' + item.locale, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: languages.map(item => /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: t('description'),\n            name: `description[${item.locale}]`,\n            valuePropName: \"data\",\n            getValueFromEvent: (event, editor) => {\n              return editor.getData();\n            },\n            rules: [{\n              required: item.locale === defaultLang,\n              message: t('required')\n            }],\n            hidden: item.locale !== defaultLang,\n            children: /*#__PURE__*/_jsxDEV(CKEditor, {\n              editor: ClassicEditor,\n              data: form.getFieldValue(`description[${item.locale}]`) || '',\n              onReady: editor => {\n                // Editor is ready to use\n              },\n              onError: (error, {\n                willEditorRestart\n              }) => {\n                if (willEditorRestart) {\n                  // Editor will restart, toolbar will be hidden automatically\n                  console.warn('CKEditor will restart due to error:', error);\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          loading: loadingBtn,\n          children: t('save')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_s(Policy, \"5IZ63ZVRBbqQSuI+pqmJorzhy0k=\", false, function () {\n  return [useTranslation, useSelector, useDispatch, Form.useForm, useSelector];\n});\n_c = Policy;\nvar _c;\n$RefreshReg$(_c, \"Policy\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "toast", "<PERSON><PERSON>", "Card", "Col", "Form", "Input", "Row", "Space", "shallowEqual", "useDispatch", "useSelector", "CKEditor", "ClassicEditor", "disable<PERSON><PERSON><PERSON><PERSON>", "setMenuData", "privacyService", "useTranslation", "Loading", "LanguageList", "getTranslationFields", "jsxDEV", "_jsxDEV", "Policy", "_s", "t", "activeMenu", "state", "menu", "dispatch", "form", "useForm", "loadingBtn", "setLoadingBtn", "loading", "setLoading", "languages", "defaultLang", "formLang", "data", "getFieldsValue", "getLanguageFields", "translations", "result", "map", "item", "_translations$find", "_translations$find2", "locale", "find", "el", "title", "description", "Object", "assign", "fetchPolicy", "getPolicy", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catch", "dismiss", "finally", "refetch", "onFinish", "values", "body", "createPolicy", "success", "extra", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "name", "layout", "initialValues", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "message", "hidden", "valuePropName", "getValueFromEvent", "event", "editor", "getData", "getFieldValue", "onReady", "onError", "error", "willEditorRestart", "console", "warn", "type", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/privacy/policy.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { Button, Card, Col, Form, Input, Row, Space } from 'antd';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { CKEditor } from '@ckeditor/ckeditor5-react';\nimport ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nimport { disableRefetch, setMenuData } from 'redux/slices/menu';\nimport privacyService from 'services/privacy';\nimport { useTranslation } from 'react-i18next';\nimport Loading from 'components/loading';\nimport LanguageList from 'components/language-list';\nimport getTranslationFields from 'helpers/getTranslationFields';\n\nexport default function Policy() {\n  const { t } = useTranslation();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const { languages, defaultLang } = useSelector(\n    (state) => state.formLang,\n    shallowEqual,\n  );\n\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({ activeMenu, data }));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  function getLanguageFields(data) {\n    if (!data) {\n      return {};\n    }\n    const { translations } = data;\n    const result = languages.map((item) => ({\n      [`title[${item.locale}]`]: translations.find(\n        (el) => el.locale === item.locale,\n      )?.title,\n      [`description[${item.locale}]`]: translations.find(\n        (el) => el.locale === item.locale,\n      )?.description,\n    }));\n    return Object.assign({}, ...result);\n  }\n\n  function fetchPolicy() {\n    setLoading(true);\n    privacyService\n      .getPolicy()\n      .then(({ data }) =>\n        form.setFieldsValue({\n          ...getLanguageFields(data),\n        }),\n      )\n      .catch(() => {\n        toast.dismiss(404);\n      })\n      .finally(() => {\n        setLoading(false);\n        dispatch(disableRefetch(activeMenu));\n      });\n  }\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchPolicy();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [activeMenu.refetch]);\n\n  const onFinish = (values) => {\n    const body = {\n      title: getTranslationFields(languages, values),\n      description: getTranslationFields(languages, values, 'description'),\n    };\n    setLoadingBtn(true);\n    privacyService\n      .createPolicy(body)\n      .then(() => {\n        toast.success(t('successfully.saved'));\n      })\n      .finally(() => setLoadingBtn(false));\n  };\n\n  return (\n    <Card title={t('policy')} extra={<LanguageList />}>\n      {!loading ? (\n        <Form\n          name='policy-form'\n          layout='vertical'\n          onFinish={onFinish}\n          form={form}\n          initialValues={activeMenu.data}\n        >\n          <Row gutter={12}>\n            <Col span={12}>\n              {languages.map((item) => (\n                <Form.Item\n                  key={'title' + item.locale}\n                  label={t('title')}\n                  name={`title[${item.locale}]`}\n                  rules={[\n                    {\n                      required: item?.locale === defaultLang,\n                      message: t('required'),\n                    },\n                  ]}\n                  hidden={item.locale !== defaultLang}\n                >\n                  <Input />\n                </Form.Item>\n              ))}\n            </Col>\n            <Col span={24}>\n              {languages.map((item) => (\n                <Form.Item\n                  label={t('description')}\n                  name={`description[${item.locale}]`}\n                  valuePropName='data'\n                  getValueFromEvent={(event, editor) => {\n                    return editor.getData();\n                  }}\n                  rules={[\n                    {\n                      required: item.locale === defaultLang,\n                      message: t('required'),\n                    },\n                  ]}\n                  hidden={item.locale !== defaultLang}\n                >\n                  <CKEditor\n                    editor={ClassicEditor}\n                    data={form.getFieldValue(`description[${item.locale}]`) || ''}\n                    onReady={(editor) => {\n                      // Editor is ready to use\n                    }}\n                    onError={(error, { willEditorRestart }) => {\n                      if (willEditorRestart) {\n                        // Editor will restart, toolbar will be hidden automatically\n                        console.warn('CKEditor will restart due to error:', error);\n                      }\n                    }}\n                  />\n                </Form.Item>\n              ))}\n            </Col>\n          </Row>\n          <Space>\n            <Button type='primary' htmlType='submit' loading={loadingBtn}>\n              {t('save')}\n            </Button>\n          </Space>\n        </Form>\n      ) : (\n        <Loading />\n      )}\n    </Card>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AACjE,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAC/D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,oBAAoB,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,eAAe,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAE,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAW,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnB,YAAY,CAAC;EACvE,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEoC,SAAS;IAAEC;EAAY,CAAC,GAAG1B,WAAW,CAC3CgB,KAAK,IAAKA,KAAK,CAACW,QAAQ,EACzB7B,YACF,CAAC;EAEDV,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,MAAMwC,IAAI,GAAGT,IAAI,CAACU,cAAc,CAAC,IAAI,CAAC;MACtCX,QAAQ,CAACd,WAAW,CAAC;QAAEW,UAAU;QAAEa;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,SAASE,iBAAiBA,CAACF,IAAI,EAAE;IAC/B,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,CAAC,CAAC;IACX;IACA,MAAM;MAAEG;IAAa,CAAC,GAAGH,IAAI;IAC7B,MAAMI,MAAM,GAAGP,SAAS,CAACQ,GAAG,CAAEC,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA;MAAA,OAAM;QACtC,CAAE,SAAQF,IAAI,CAACG,MAAO,GAAE,IAAAF,kBAAA,GAAGJ,YAAY,CAACO,IAAI,CACzCC,EAAE,IAAKA,EAAE,CAACF,MAAM,KAAKH,IAAI,CAACG,MAC7B,CAAC,cAAAF,kBAAA,uBAF0BA,kBAAA,CAExBK,KAAK;QACR,CAAE,eAAcN,IAAI,CAACG,MAAO,GAAE,IAAAD,mBAAA,GAAGL,YAAY,CAACO,IAAI,CAC/CC,EAAE,IAAKA,EAAE,CAACF,MAAM,KAAKH,IAAI,CAACG,MAC7B,CAAC,cAAAD,mBAAA,uBAFgCA,mBAAA,CAE9BK;MACL,CAAC;IAAA,CAAC,CAAC;IACH,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGX,MAAM,CAAC;EACrC;EAEA,SAASY,WAAWA,CAAA,EAAG;IACrBpB,UAAU,CAAC,IAAI,CAAC;IAChBnB,cAAc,CACXwC,SAAS,CAAC,CAAC,CACXC,IAAI,CAAC,CAAC;MAAElB;IAAK,CAAC,KACbT,IAAI,CAAC4B,cAAc,CAAC;MAClB,GAAGjB,iBAAiB,CAACF,IAAI;IAC3B,CAAC,CACH,CAAC,CACAoB,KAAK,CAAC,MAAM;MACX1D,KAAK,CAAC2D,OAAO,CAAC,GAAG,CAAC;IACpB,CAAC,CAAC,CACDC,OAAO,CAAC,MAAM;MACb1B,UAAU,CAAC,KAAK,CAAC;MACjBN,QAAQ,CAACf,cAAc,CAACY,UAAU,CAAC,CAAC;IACtC,CAAC,CAAC;EACN;EAEA3B,SAAS,CAAC,MAAM;IACd,IAAI2B,UAAU,CAACoC,OAAO,EAAE;MACtBP,WAAW,CAAC,CAAC;IACf;IACA;EACF,CAAC,EAAE,CAAC7B,UAAU,CAACoC,OAAO,CAAC,CAAC;EAExB,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IAC3B,MAAMC,IAAI,GAAG;MACXd,KAAK,EAAE/B,oBAAoB,CAACgB,SAAS,EAAE4B,MAAM,CAAC;MAC9CZ,WAAW,EAAEhC,oBAAoB,CAACgB,SAAS,EAAE4B,MAAM,EAAE,aAAa;IACpE,CAAC;IACD/B,aAAa,CAAC,IAAI,CAAC;IACnBjB,cAAc,CACXkD,YAAY,CAACD,IAAI,CAAC,CAClBR,IAAI,CAAC,MAAM;MACVxD,KAAK,CAACkE,OAAO,CAAC1C,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACxC,CAAC,CAAC,CACDoC,OAAO,CAAC,MAAM5B,aAAa,CAAC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,oBACEX,OAAA,CAACnB,IAAI;IAACgD,KAAK,EAAE1B,CAAC,CAAC,QAAQ,CAAE;IAAC2C,KAAK,eAAE9C,OAAA,CAACH,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAAAC,QAAA,EAC/C,CAACvC,OAAO,gBACPZ,OAAA,CAACjB,IAAI;MACHqE,IAAI,EAAC,aAAa;MAClBC,MAAM,EAAC,UAAU;MACjBZ,QAAQ,EAAEA,QAAS;MACnBjC,IAAI,EAAEA,IAAK;MACX8C,aAAa,EAAElD,UAAU,CAACa,IAAK;MAAAkC,QAAA,gBAE/BnD,OAAA,CAACf,GAAG;QAACsE,MAAM,EAAE,EAAG;QAAAJ,QAAA,gBACdnD,OAAA,CAAClB,GAAG;UAAC0E,IAAI,EAAE,EAAG;UAAAL,QAAA,EACXrC,SAAS,CAACQ,GAAG,CAAEC,IAAI,iBAClBvB,OAAA,CAACjB,IAAI,CAAC0E,IAAI;YAERC,KAAK,EAAEvD,CAAC,CAAC,OAAO,CAAE;YAClBiD,IAAI,EAAG,SAAQ7B,IAAI,CAACG,MAAO,GAAG;YAC9BiC,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,MAAM,MAAKX,WAAW;cACtC8C,OAAO,EAAE1D,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YACF2D,MAAM,EAAEvC,IAAI,CAACG,MAAM,KAAKX,WAAY;YAAAoC,QAAA,eAEpCnD,OAAA,CAAChB,KAAK;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAXJ,OAAO,GAAG3B,IAAI,CAACG,MAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYjB,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlD,OAAA,CAAClB,GAAG;UAAC0E,IAAI,EAAE,EAAG;UAAAL,QAAA,EACXrC,SAAS,CAACQ,GAAG,CAAEC,IAAI,iBAClBvB,OAAA,CAACjB,IAAI,CAAC0E,IAAI;YACRC,KAAK,EAAEvD,CAAC,CAAC,aAAa,CAAE;YACxBiD,IAAI,EAAG,eAAc7B,IAAI,CAACG,MAAO,GAAG;YACpCqC,aAAa,EAAC,MAAM;YACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;cACpC,OAAOA,MAAM,CAACC,OAAO,CAAC,CAAC;YACzB,CAAE;YACFR,KAAK,EAAE,CACL;cACEC,QAAQ,EAAErC,IAAI,CAACG,MAAM,KAAKX,WAAW;cACrC8C,OAAO,EAAE1D,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YACF2D,MAAM,EAAEvC,IAAI,CAACG,MAAM,KAAKX,WAAY;YAAAoC,QAAA,eAEpCnD,OAAA,CAACV,QAAQ;cACP4E,MAAM,EAAE3E,aAAc;cACtB0B,IAAI,EAAET,IAAI,CAAC4D,aAAa,CAAE,eAAc7C,IAAI,CAACG,MAAO,GAAE,CAAC,IAAI,EAAG;cAC9D2C,OAAO,EAAGH,MAAM,IAAK;gBACnB;cAAA,CACA;cACFI,OAAO,EAAEA,CAACC,KAAK,EAAE;gBAAEC;cAAkB,CAAC,KAAK;gBACzC,IAAIA,iBAAiB,EAAE;kBACrB;kBACAC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEH,KAAK,CAAC;gBAC5D;cACF;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlD,OAAA,CAACd,KAAK;QAAAiE,QAAA,eACJnD,OAAA,CAACpB,MAAM;UAAC+F,IAAI,EAAC,SAAS;UAACC,QAAQ,EAAC,QAAQ;UAAChE,OAAO,EAAEF,UAAW;UAAAyC,QAAA,EAC1DhD,CAAC,CAAC,MAAM;QAAC;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAEPlD,OAAA,CAACJ,OAAO;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX;AAAChD,EAAA,CArJuBD,MAAM;EAAA,QACdN,cAAc,EACLN,WAAW,EACjBD,WAAW,EACbL,IAAI,CAAC0B,OAAO,EAGQpB,WAAW;AAAA;AAAAwF,EAAA,GAPxB5E,MAAM;AAAA,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}