{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\currencies\\\\currency-form.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Button, Col, Form, Input, InputNumber, Row, Select, Switch } from 'antd';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport currency from '../../helpers/currnecy.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CurrencyForm({\n  form,\n  handleSubmit,\n  isDefault = false\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n\n  // states\n  const [loadingBtn, setLoadingBtn] = useState(false);\n\n  // constants\n  const options = currency.map(item => {\n    var _item$name;\n    return {\n      label: `${item === null || item === void 0 ? void 0 : (_item$name = item.name) === null || _item$name === void 0 ? void 0 : _item$name.toUpperCase()} ( ${item === null || item === void 0 ? void 0 : item.symbol_native} )`,\n      value: item === null || item === void 0 ? void 0 : item.code,\n      symbol: item === null || item === void 0 ? void 0 : item.symbol_native\n    };\n  });\n\n  // submit form\n  const onFinish = values => {\n    setLoadingBtn(true);\n    handleSubmit(values).finally(() => setLoadingBtn(false));\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    name: \"currency-form\",\n    onFinish: onFinish,\n    form: form,\n    layout: \"vertical\",\n    initialValues: {\n      ...activeMenu.data,\n      active: true,\n      position: 'before'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 12,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('title'),\n          name: \"title\",\n          rules: [{\n            required: true,\n            message: t('required')\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            onChange: (e, i) => form.setFieldsValue({\n              symbol: i.symbol\n            }),\n            filterOption: (input, option) => {\n              var _option$label;\n              return ((_option$label = option === null || option === void 0 ? void 0 : option.label) !== null && _option$label !== void 0 ? _option$label : '').toLowerCase().includes(input.toLowerCase());\n            },\n            filterSort: (optionA, optionB) => {\n              var _optionA$label, _optionB$label;\n              return ((_optionA$label = optionA === null || optionA === void 0 ? void 0 : optionA.label) !== null && _optionA$label !== void 0 ? _optionA$label : '').toLowerCase().localeCompare(((_optionB$label = optionB === null || optionB === void 0 ? void 0 : optionB.label) !== null && _optionB$label !== void 0 ? _optionB$label : '').toLowerCase());\n            },\n            showSearch: true,\n            allowClear: true,\n            options: options\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('symbol'),\n          name: \"symbol\",\n          rules: [{\n            required: true,\n            message: t('required')\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('rate'),\n          name: \"rate\",\n          rules: [{\n            required: true,\n            message: t('required')\n          }, {\n            type: 'number',\n            min: 0,\n            message: t('must.be.positive')\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            className: \"w-100\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('symbol_position'),\n          name: \"position\",\n          rules: [{\n            required: true,\n            message: t('required')\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            onChange: value => form.setFieldsValue({\n              position: value\n            }),\n            options: [{\n              label: t('after'),\n              value: 'after'\n            }, {\n              label: t('before'),\n              value: 'before'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: t('active'),\n          name: \"active\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            disabled: isDefault\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      htmlType: \"submit\",\n      loading: loadingBtn,\n      children: t('submit')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}\n_s(CurrencyForm, \"wQ0GyiVvE0WVSfJLj3AvYqZCb5w=\", false, function () {\n  return [useTranslation, useSelector];\n});\n_c = CurrencyForm;\nvar _c;\n$RefreshReg$(_c, \"CurrencyForm\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Col", "Form", "Input", "InputNumber", "Row", "Select", "Switch", "shallowEqual", "useSelector", "useTranslation", "currency", "jsxDEV", "_jsxDEV", "CurrencyForm", "form", "handleSubmit", "isDefault", "_s", "t", "activeMenu", "state", "menu", "loadingBtn", "setLoadingBtn", "options", "map", "item", "_item$name", "label", "name", "toUpperCase", "symbol_native", "value", "code", "symbol", "onFinish", "values", "finally", "layout", "initialValues", "data", "active", "position", "children", "gutter", "span", "<PERSON><PERSON>", "rules", "required", "message", "onChange", "e", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterOption", "input", "option", "_option$label", "toLowerCase", "includes", "filterSort", "optionA", "optionB", "_optionA$label", "_optionB$label", "localeCompare", "showSearch", "allowClear", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "type", "min", "className", "valuePropName", "htmlType", "loading", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/currencies/currency-form.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Button,\n  Col,\n  Form,\n  Input,\n  InputNumber,\n  Row,\n  Select,\n  Switch,\n} from 'antd';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport currency from '../../helpers/currnecy.json';\n\nexport default function CurrencyForm({\n  form,\n  handleSubmit,\n  isDefault = false,\n}) {\n  const { t } = useTranslation();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  // states\n  const [loadingBtn, setLoadingBtn] = useState(false);\n\n  // constants\n  const options = currency.map((item) => ({\n    label: `${item?.name?.toUpperCase()} ( ${item?.symbol_native} )`,\n    value: item?.code,\n    symbol: item?.symbol_native,\n  }));\n\n  // submit form\n  const onFinish = (values) => {\n    setLoadingBtn(true);\n    handleSubmit(values).finally(() => setLoadingBtn(false));\n  };\n\n  return (\n    <Form\n      name='currency-form'\n      onFinish={onFinish}\n      form={form}\n      layout='vertical'\n      initialValues={{\n        ...activeMenu.data,\n        active: true,\n        position: 'before',\n      }}\n    >\n      <Row gutter={12}>\n        <Col span={12}>\n          <Form.Item\n            label={t('title')}\n            name='title'\n            rules={[\n              {\n                required: true,\n                message: t('required'),\n              },\n            ]}\n          >\n            <Select\n              onChange={(e, i) => form.setFieldsValue({ symbol: i.symbol })}\n              filterOption={(input, option) =>\n                (option?.label ?? '')\n                  .toLowerCase()\n                  .includes(input.toLowerCase())\n              }\n              filterSort={(optionA, optionB) =>\n                (optionA?.label ?? '')\n                  .toLowerCase()\n                  .localeCompare((optionB?.label ?? '').toLowerCase())\n              }\n              showSearch\n              allowClear\n              options={options}\n            />\n          </Form.Item>\n        </Col>\n\n        <Col span={12}>\n          <Form.Item\n            label={t('symbol')}\n            name='symbol'\n            rules={[\n              {\n                required: true,\n                message: t('required'),\n              },\n            ]}\n          >\n            <Input disabled />\n          </Form.Item>\n        </Col>\n\n        <Col span={12}>\n          <Form.Item\n            label={t('rate')}\n            name='rate'\n            rules={[\n              {\n                required: true,\n                message: t('required'),\n              },\n              {\n                type: 'number',\n                min: 0,\n                message: t('must.be.positive'),\n              },\n            ]}\n          >\n            <InputNumber className='w-100' />\n          </Form.Item>\n        </Col>\n\n        <Col span={12}>\n          <Form.Item\n            label={t('symbol_position')}\n            name='position'\n            rules={[\n              {\n                required: true,\n                message: t('required'),\n              },\n            ]}\n          >\n            <Select\n              onChange={(value) => form.setFieldsValue({ position: value })}\n              options={[\n                { label: t('after'), value: 'after' },\n                { label: t('before'), value: 'before' },\n              ]}\n            />\n          </Form.Item>\n        </Col>\n\n        <Col span={12}>\n          <Form.Item label={t('active')} name='active' valuePropName='checked'>\n            <Switch disabled={isDefault} />\n          </Form.Item>\n        </Col>\n      </Row>\n      <Button type='primary' htmlType='submit' loading={loadingBtn}>\n        {t('submit')}\n      </Button>\n    </Form>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,GAAG,EACHC,MAAM,EACNC,MAAM,QACD,MAAM;AACb,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,QAAQ,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,YAAYA,CAAC;EACnCC,IAAI;EACJC,YAAY;EACZC,SAAS,GAAG;AACd,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM;IAAEC;EAAE,CAAC,GAAGT,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAW,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEd,YAAY,CAAC;;EAEvE;EACA,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM0B,OAAO,GAAGd,QAAQ,CAACe,GAAG,CAAEC,IAAI;IAAA,IAAAC,UAAA;IAAA,OAAM;MACtCC,KAAK,EAAG,GAAEF,IAAI,aAAJA,IAAI,wBAAAC,UAAA,GAAJD,IAAI,CAAEG,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYG,WAAW,CAAC,CAAE,MAAKJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,aAAc,IAAG;MAChEC,KAAK,EAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI;MACjBC,MAAM,EAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK;IAChB,CAAC;EAAA,CAAC,CAAC;;EAEH;EACA,MAAMI,QAAQ,GAAIC,MAAM,IAAK;IAC3Bb,aAAa,CAAC,IAAI,CAAC;IACnBR,YAAY,CAACqB,MAAM,CAAC,CAACC,OAAO,CAAC,MAAMd,aAAa,CAAC,KAAK,CAAC,CAAC;EAC1D,CAAC;EAED,oBACEX,OAAA,CAACX,IAAI;IACH4B,IAAI,EAAC,eAAe;IACpBM,QAAQ,EAAEA,QAAS;IACnBrB,IAAI,EAAEA,IAAK;IACXwB,MAAM,EAAC,UAAU;IACjBC,aAAa,EAAE;MACb,GAAGpB,UAAU,CAACqB,IAAI;MAClBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEF/B,OAAA,CAACR,GAAG;MAACwC,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACd/B,OAAA,CAACZ,GAAG;QAAC6C,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/B,OAAA,CAACX,IAAI,CAAC6C,IAAI;UACRlB,KAAK,EAAEV,CAAC,CAAC,OAAO,CAAE;UAClBW,IAAI,EAAC,OAAO;UACZkB,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAE/B,CAAC,CAAC,UAAU;UACvB,CAAC,CACD;UAAAyB,QAAA,eAEF/B,OAAA,CAACP,MAAM;YACL6C,QAAQ,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKtC,IAAI,CAACuC,cAAc,CAAC;cAAEnB,MAAM,EAAEkB,CAAC,CAAClB;YAAO,CAAC,CAAE;YAC9DoB,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;cAAA,IAAAC,aAAA;cAAA,OAC1B,EAAAA,aAAA,GAACD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE5B,KAAK,cAAA6B,aAAA,cAAAA,aAAA,GAAI,EAAE,EACjBC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC;YAAA,CACjC;YACDE,UAAU,EAAEA,CAACC,OAAO,EAAEC,OAAO;cAAA,IAAAC,cAAA,EAAAC,cAAA;cAAA,OAC3B,EAAAD,cAAA,GAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjC,KAAK,cAAAmC,cAAA,cAAAA,cAAA,GAAI,EAAE,EAClBL,WAAW,CAAC,CAAC,CACbO,aAAa,CAAC,EAAAD,cAAA,GAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAElC,KAAK,cAAAoC,cAAA,cAAAA,cAAA,GAAI,EAAE,EAAEN,WAAW,CAAC,CAAC,CAAC;YAAA,CACvD;YACDQ,UAAU;YACVC,UAAU;YACV3C,OAAO,EAAEA;UAAQ;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN3D,OAAA,CAACZ,GAAG;QAAC6C,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/B,OAAA,CAACX,IAAI,CAAC6C,IAAI;UACRlB,KAAK,EAAEV,CAAC,CAAC,QAAQ,CAAE;UACnBW,IAAI,EAAC,QAAQ;UACbkB,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAE/B,CAAC,CAAC,UAAU;UACvB,CAAC,CACD;UAAAyB,QAAA,eAEF/B,OAAA,CAACV,KAAK;YAACsE,QAAQ;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN3D,OAAA,CAACZ,GAAG;QAAC6C,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/B,OAAA,CAACX,IAAI,CAAC6C,IAAI;UACRlB,KAAK,EAAEV,CAAC,CAAC,MAAM,CAAE;UACjBW,IAAI,EAAC,MAAM;UACXkB,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAE/B,CAAC,CAAC,UAAU;UACvB,CAAC,EACD;YACEuD,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC;YACNzB,OAAO,EAAE/B,CAAC,CAAC,kBAAkB;UAC/B,CAAC,CACD;UAAAyB,QAAA,eAEF/B,OAAA,CAACT,WAAW;YAACwE,SAAS,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN3D,OAAA,CAACZ,GAAG;QAAC6C,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/B,OAAA,CAACX,IAAI,CAAC6C,IAAI;UACRlB,KAAK,EAAEV,CAAC,CAAC,iBAAiB,CAAE;UAC5BW,IAAI,EAAC,UAAU;UACfkB,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAE/B,CAAC,CAAC,UAAU;UACvB,CAAC,CACD;UAAAyB,QAAA,eAEF/B,OAAA,CAACP,MAAM;YACL6C,QAAQ,EAAGlB,KAAK,IAAKlB,IAAI,CAACuC,cAAc,CAAC;cAAEX,QAAQ,EAAEV;YAAM,CAAC,CAAE;YAC9DR,OAAO,EAAE,CACP;cAAEI,KAAK,EAAEV,CAAC,CAAC,OAAO,CAAC;cAAEc,KAAK,EAAE;YAAQ,CAAC,EACrC;cAAEJ,KAAK,EAAEV,CAAC,CAAC,QAAQ,CAAC;cAAEc,KAAK,EAAE;YAAS,CAAC;UACvC;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN3D,OAAA,CAACZ,GAAG;QAAC6C,IAAI,EAAE,EAAG;QAAAF,QAAA,eACZ/B,OAAA,CAACX,IAAI,CAAC6C,IAAI;UAAClB,KAAK,EAAEV,CAAC,CAAC,QAAQ,CAAE;UAACW,IAAI,EAAC,QAAQ;UAAC+C,aAAa,EAAC,SAAS;UAAAjC,QAAA,eAClE/B,OAAA,CAACN,MAAM;YAACkE,QAAQ,EAAExD;UAAU;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN3D,OAAA,CAACb,MAAM;MAAC0E,IAAI,EAAC,SAAS;MAACI,QAAQ,EAAC,QAAQ;MAACC,OAAO,EAAExD,UAAW;MAAAqB,QAAA,EAC1DzB,CAAC,CAAC,QAAQ;IAAC;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX;AAACtD,EAAA,CAtIuBJ,YAAY;EAAA,QAKpBJ,cAAc,EACLD,WAAW;AAAA;AAAAuE,EAAA,GANZlE,YAAY;AAAA,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}