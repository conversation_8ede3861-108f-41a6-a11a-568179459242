// Order Board Performance Optimizations

.order-board {
  // Use CSS containment for better performance
  contain: layout style paint;
  
  .dnd-column {
    // Enable GPU acceleration for smooth drag operations
    transform: translateZ(0);
    will-change: transform;
    
    // Optimize scrolling performance
    .scrollbar-container {
      // Use hardware acceleration for smooth scrolling
      transform: translateZ(0);
      -webkit-overflow-scrolling: touch;
    }
  }
}

.order-board-skeleton {
  display: flex;
  gap: 16px;
  padding: 16px;
  
  .dnd-column {
    flex: 1;
    min-width: 300px;
    
    .ant-card {
      // Prevent layout thrashing during loading
      contain: layout;
    }
  }
}

// Optimize order cards for better rendering performance
.order-card-seller {
  // Use CSS containment to isolate rendering
  contain: layout style;
  
  // Optimize for frequent updates
  .ant-card-body {
    // Prevent unnecessary repaints
    contain: paint;
  }
  
  // Smooth transitions for drag operations
  &.dragging {
    transform: rotate(5deg);
    transition: transform 0.2s ease;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
}

// Performance optimizations for large lists
.order-list-container {
  // Enable virtual scrolling optimizations
  overflow: auto;
  height: 100%;
  
  // Use CSS containment for better performance
  contain: strict;
  
  // Optimize scroll performance
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// Reduce paint operations during drag and drop
.react-beautiful-dnd-dragging {
  // Use GPU layer for smooth animations
  transform: translateZ(0);
  will-change: transform;
}

// Optimize loading states
.order-loading-state {
  // Prevent layout shifts during loading
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  // Use CSS containment
  contain: layout style paint;
}
