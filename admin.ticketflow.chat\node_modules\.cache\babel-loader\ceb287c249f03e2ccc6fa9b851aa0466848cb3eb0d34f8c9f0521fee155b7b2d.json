{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\settings\\\\socialSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Card, Col, Form, Input, Row } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport settingService from '../../services/settings';\nimport { toast } from 'react-toastify';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport { fetchSettings as getSettings } from '../../redux/slices/globalSettings';\nimport Loading from '../../components/loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SocialSettings() {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [form] = Form.useForm();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({\n        activeMenu,\n        data\n      }));\n    };\n  }, [activeMenu, dispatch, form]);\n  const createSettings = list => {\n    const result = list.map(item => ({\n      [item.key]: item.value\n    }));\n    return Object.assign({}, ...result);\n  };\n  useEffect(() => {\n    let isMounted = true;\n    if (activeMenu.refetch) {\n      setLoading(true);\n      settingService.get().then(res => {\n        if (isMounted) {\n          const data = createSettings(res.data);\n          form.setFieldsValue(data);\n        }\n      }).finally(() => {\n        if (isMounted) {\n          setLoading(false);\n          dispatch(disableRefetch(activeMenu));\n        }\n      });\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [activeMenu.refetch, activeMenu, dispatch, form]);\n  function updateSettings(data) {\n    setLoadingBtn(true);\n    settingService.update(data).then(() => {\n      toast.success(t('successfully.updated'));\n      dispatch(getSettings());\n    }).finally(() => setLoadingBtn(false));\n  }\n  const onFinish = values => {\n    updateSettings(values);\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    layout: \"vertical\",\n    form: form,\n    name: \"global-settings\",\n    onFinish: onFinish,\n    initialValues: {\n      ...activeMenu.data\n    },\n    children: !loading ? /*#__PURE__*/_jsxDEV(Card, {\n      title: t('social.settings'),\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => form.submit(),\n        loading: loadingBtn,\n        children: t('save')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 13\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: 'Instagram',\n            name: \"instagram\",\n            rules: [{\n              required: true,\n              message: t('required')\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: 'Facebook',\n            name: \"facebook\",\n            rules: [{\n              required: true,\n              message: t('required')\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: 'Twitter',\n            name: \"twitter\",\n            rules: [{\n              required: true,\n              message: t('required')\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_s(SocialSettings, \"rqBjAalTt4Gd9d1a8n3VPa1DMdc=\", false, function () {\n  return [useTranslation, Form.useForm, useSelector, useDispatch];\n});\n_c = SocialSettings;\nvar _c;\n$RefreshReg$(_c, \"SocialSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Card", "Col", "Form", "Input", "Row", "useTranslation", "settingService", "toast", "shallowEqual", "useDispatch", "useSelector", "disable<PERSON><PERSON><PERSON><PERSON>", "setMenuData", "fetchSettings", "getSettings", "Loading", "jsxDEV", "_jsxDEV", "SocialSettings", "_s", "t", "form", "useForm", "activeMenu", "state", "menu", "dispatch", "loading", "setLoading", "loadingBtn", "setLoadingBtn", "data", "getFieldsValue", "createSettings", "list", "result", "map", "item", "key", "value", "Object", "assign", "isMounted", "refetch", "get", "then", "res", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finally", "updateSettings", "update", "success", "onFinish", "values", "layout", "name", "initialValues", "children", "title", "extra", "type", "onClick", "submit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "message", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/settings/socialSettings.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Button, Card, Col, Form, Input, Row } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport settingService from '../../services/settings';\nimport { toast } from 'react-toastify';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { disableRefetch, setMenuData } from '../../redux/slices/menu';\nimport { fetchSettings as getSettings } from '../../redux/slices/globalSettings';\nimport Loading from '../../components/loading';\n\nexport default function SocialSettings() {\n  const { t } = useTranslation();\n  const [form] = Form.useForm();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const dispatch = useDispatch();\n\n  const [loading, setLoading] = useState(false);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n\n  useEffect(() => {\n    return () => {\n      const data = form.getFieldsValue(true);\n      dispatch(setMenuData({ activeMenu, data }));\n    };\n  }, [activeMenu, dispatch, form]);\n\n  const createSettings = (list) => {\n    const result = list.map((item) => ({\n      [item.key]: item.value,\n    }));\n    return Object.assign({}, ...result);\n  };\n\n\n\n  useEffect(() => {\n    let isMounted = true;\n\n    if (activeMenu.refetch) {\n      setLoading(true);\n      settingService\n        .get()\n        .then((res) => {\n          if (isMounted) {\n            const data = createSettings(res.data);\n            form.setFieldsValue(data);\n          }\n        })\n        .finally(() => {\n          if (isMounted) {\n            setLoading(false);\n            dispatch(disableRefetch(activeMenu));\n          }\n        });\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [activeMenu.refetch, activeMenu, dispatch, form]);\n\n  function updateSettings(data) {\n    setLoadingBtn(true);\n    settingService\n      .update(data)\n      .then(() => {\n        toast.success(t('successfully.updated'));\n        dispatch(getSettings());\n      })\n      .finally(() => setLoadingBtn(false));\n  }\n\n  const onFinish = (values) => {\n    updateSettings(values);\n  };\n\n  return (\n    <Form\n      layout='vertical'\n      form={form}\n      name='global-settings'\n      onFinish={onFinish}\n      initialValues={{ ...activeMenu.data }}\n    >\n      {!loading ? (\n        <Card\n          title={t('social.settings')}\n          extra={\n            <Button\n              type='primary'\n              onClick={() => form.submit()}\n              loading={loadingBtn}\n            >\n              {t('save')}\n            </Button>\n          }\n        >\n          <Row gutter={12}>\n            <Col span={12}>\n              <Form.Item\n                label={'Instagram'}\n                name='instagram'\n                rules={[\n                  {\n                    required: true,\n                    message: t('required'),\n                  },\n                ]}\n              >\n                <Input />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label={'Facebook'}\n                name='facebook'\n                rules={[\n                  {\n                    required: true,\n                    message: t('required'),\n                  },\n                ]}\n              >\n                <Input />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label={'Twitter'}\n                name='twitter'\n                rules={[\n                  {\n                    required: true,\n                    message: t('required'),\n                  },\n                ]}\n              >\n                <Input />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Card>\n      ) : (\n        <Loading />\n      )}\n    </Form>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACrE,SAASC,aAAa,IAAIC,WAAW,QAAQ,mCAAmC;AAChF,OAAOC,OAAO,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAE,CAAC,GAAGf,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACgB,IAAI,CAAC,GAAGnB,IAAI,CAACoB,OAAO,CAAC,CAAC;EAC7B,MAAM;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEjB,YAAY,CAAC;EACvE,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEnDD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,MAAMkC,IAAI,GAAGV,IAAI,CAACW,cAAc,CAAC,IAAI,CAAC;MACtCN,QAAQ,CAACd,WAAW,CAAC;QAAEW,UAAU;QAAEQ;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACR,UAAU,EAAEG,QAAQ,EAAEL,IAAI,CAAC,CAAC;EAEhC,MAAMY,cAAc,GAAIC,IAAI,IAAK;IAC/B,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,IAAI,KAAM;MACjC,CAACA,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE;IACnB,CAAC,CAAC,CAAC;IACH,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGN,MAAM,CAAC;EACrC,CAAC;EAIDtC,SAAS,CAAC,MAAM;IACd,IAAI6C,SAAS,GAAG,IAAI;IAEpB,IAAInB,UAAU,CAACoB,OAAO,EAAE;MACtBf,UAAU,CAAC,IAAI,CAAC;MAChBtB,cAAc,CACXsC,GAAG,CAAC,CAAC,CACLC,IAAI,CAAEC,GAAG,IAAK;QACb,IAAIJ,SAAS,EAAE;UACb,MAAMX,IAAI,GAAGE,cAAc,CAACa,GAAG,CAACf,IAAI,CAAC;UACrCV,IAAI,CAAC0B,cAAc,CAAChB,IAAI,CAAC;QAC3B;MACF,CAAC,CAAC,CACDiB,OAAO,CAAC,MAAM;QACb,IAAIN,SAAS,EAAE;UACbd,UAAU,CAAC,KAAK,CAAC;UACjBF,QAAQ,CAACf,cAAc,CAACY,UAAU,CAAC,CAAC;QACtC;MACF,CAAC,CAAC;IACN;IAEA,OAAO,MAAM;MACXmB,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACnB,UAAU,CAACoB,OAAO,EAAEpB,UAAU,EAAEG,QAAQ,EAAEL,IAAI,CAAC,CAAC;EAEpD,SAAS4B,cAAcA,CAAClB,IAAI,EAAE;IAC5BD,aAAa,CAAC,IAAI,CAAC;IACnBxB,cAAc,CACX4C,MAAM,CAACnB,IAAI,CAAC,CACZc,IAAI,CAAC,MAAM;MACVtC,KAAK,CAAC4C,OAAO,CAAC/B,CAAC,CAAC,sBAAsB,CAAC,CAAC;MACxCM,QAAQ,CAACZ,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CACDkC,OAAO,CAAC,MAAMlB,aAAa,CAAC,KAAK,CAAC,CAAC;EACxC;EAEA,MAAMsB,QAAQ,GAAIC,MAAM,IAAK;IAC3BJ,cAAc,CAACI,MAAM,CAAC;EACxB,CAAC;EAED,oBACEpC,OAAA,CAACf,IAAI;IACHoD,MAAM,EAAC,UAAU;IACjBjC,IAAI,EAAEA,IAAK;IACXkC,IAAI,EAAC,iBAAiB;IACtBH,QAAQ,EAAEA,QAAS;IACnBI,aAAa,EAAE;MAAE,GAAGjC,UAAU,CAACQ;IAAK,CAAE;IAAA0B,QAAA,EAErC,CAAC9B,OAAO,gBACPV,OAAA,CAACjB,IAAI;MACH0D,KAAK,EAAEtC,CAAC,CAAC,iBAAiB,CAAE;MAC5BuC,KAAK,eACH1C,OAAA,CAAClB,MAAM;QACL6D,IAAI,EAAC,SAAS;QACdC,OAAO,EAAEA,CAAA,KAAMxC,IAAI,CAACyC,MAAM,CAAC,CAAE;QAC7BnC,OAAO,EAAEE,UAAW;QAAA4B,QAAA,EAEnBrC,CAAC,CAAC,MAAM;MAAC;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACT;MAAAT,QAAA,eAEDxC,OAAA,CAACb,GAAG;QAAC+D,MAAM,EAAE,EAAG;QAAAV,QAAA,gBACdxC,OAAA,CAAChB,GAAG;UAACmE,IAAI,EAAE,EAAG;UAAAX,QAAA,eACZxC,OAAA,CAACf,IAAI,CAACmE,IAAI;YACRC,KAAK,EAAE,WAAY;YACnBf,IAAI,EAAC,WAAW;YAChBgB,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,IAAI;cACdC,OAAO,EAAErD,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YAAAqC,QAAA,eAEFxC,OAAA,CAACd,KAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNjD,OAAA,CAAChB,GAAG;UAACmE,IAAI,EAAE,EAAG;UAAAX,QAAA,eACZxC,OAAA,CAACf,IAAI,CAACmE,IAAI;YACRC,KAAK,EAAE,UAAW;YAClBf,IAAI,EAAC,UAAU;YACfgB,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,IAAI;cACdC,OAAO,EAAErD,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YAAAqC,QAAA,eAEFxC,OAAA,CAACd,KAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNjD,OAAA,CAAChB,GAAG;UAACmE,IAAI,EAAE,EAAG;UAAAX,QAAA,eACZxC,OAAA,CAACf,IAAI,CAACmE,IAAI;YACRC,KAAK,EAAE,SAAU;YACjBf,IAAI,EAAC,SAAS;YACdgB,KAAK,EAAE,CACL;cACEC,QAAQ,EAAE,IAAI;cACdC,OAAO,EAAErD,CAAC,CAAC,UAAU;YACvB,CAAC,CACD;YAAAqC,QAAA,eAEFxC,OAAA,CAACd,KAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,gBAEPjD,OAAA,CAACF,OAAO;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACX;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX;AAAC/C,EAAA,CAzIuBD,cAAc;EAAA,QACtBb,cAAc,EACbH,IAAI,CAACoB,OAAO,EACJZ,WAAW,EACjBD,WAAW;AAAA;AAAAiE,EAAA,GAJNxD,cAAc;AAAA,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}