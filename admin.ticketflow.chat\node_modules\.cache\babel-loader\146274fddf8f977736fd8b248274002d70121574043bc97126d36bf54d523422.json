{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\drawing-map.js\",\n  _s = $RefreshSig$();\nimport { Map, Marker, Polygon, Polyline } from 'google-maps-react';\nimport React from 'react';\nimport { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport getDefaultCenter from 'helpers/getDefaultCenter';\nimport { withGoogleMaps } from './GoogleMapsWrapper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst mapApiKey = getMapApiKey();\nconst defaultCenter = getDefaultCenter();\nconst DrawingManager = props => {\n  _s();\n  // Memoize the raw coordinates to prevent infinite re-renders\n  const rawCoords = useMemo(() => {\n    return Array.isArray(props.triangleCoords) ? props.triangleCoords : [];\n  }, [props.triangleCoords]);\n\n  // Memoize the coordinate transformation to prevent infinite re-renders\n  const validTriangleCoords = useMemo(() => {\n    return rawCoords.map(coord => {\n      if (coord && typeof coord === 'object' && coord.lat !== undefined && coord.lng !== undefined) {\n        return {\n          lat: Number(coord.lat),\n          lng: Number(coord.lng)\n        };\n      }\n      return coord;\n    }).filter(coord => coord && typeof coord === 'object' && !isNaN(coord.lat) && !isNaN(coord.lng));\n  }, [rawCoords]);\n  const [markers, setMarkers] = useState(validTriangleCoords);\n  const [center, setCenter] = useState(defaultCenter);\n  const [polygon, setPolygon] = useState(validTriangleCoords);\n  const [finish, setFinish] = useState(validTriangleCoords.length > 0);\n  const [focus, setFocus] = useState(false);\n  const mapRef = useRef(null);\n  const isMountedRef = useRef(true);\n\n  // Track component mounting state\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  // Update markers when triangleCoords prop changes\n  useEffect(() => {\n    if (isMountedRef.current) {\n      setMarkers(validTriangleCoords);\n    }\n  }, [validTriangleCoords]);\n  useEffect(() => {\n    if (isMountedRef.current && props.setMerge) {\n      props.setMerge(finish);\n    }\n  }, [finish, props]);\n  const onClick = (t, map, cord) => {\n    if (!isMountedRef.current) return;\n    setFocus(false);\n    const {\n      latLng\n    } = cord;\n    const lat = latLng.lat();\n    const lng = latLng.lng();\n    if (finish) {\n      setPolygon([]);\n      props.settriangleCoords([{\n        lat,\n        lng\n      }]);\n      setCenter({\n        lat,\n        lng\n      });\n      setFinish(false);\n    } else {\n      props.settriangleCoords(prev => [...prev, {\n        lat,\n        lng\n      }]);\n    }\n  };\n  const onFinish = e => {\n    var _validTriangleCoords$, _e$position;\n    if (!isMountedRef.current) return;\n    setFinish(validTriangleCoords.length > 0);\n    if (((_validTriangleCoords$ = validTriangleCoords[0]) === null || _validTriangleCoords$ === void 0 ? void 0 : _validTriangleCoords$.lat) === ((_e$position = e.position) === null || _e$position === void 0 ? void 0 : _e$position.lat) && validTriangleCoords.length > 1) {\n      setPolygon(validTriangleCoords);\n      props.setLocation && props.setLocation(validTriangleCoords);\n      setFinish(true);\n      setFocus(true);\n    }\n  };\n  const currentLocation = () => {\n    if (!isMountedRef.current) return;\n    navigator.geolocation.getCurrentPosition(function (position) {\n      if (isMountedRef.current) {\n        setCenter({\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        });\n      }\n    }, function (error) {\n      console.error('Error getting current location:', error);\n    });\n  };\n  useEffect(() => {\n    setFocus(true);\n  }, []);\n  function handleMapReady(_, map) {\n    if (!isMountedRef.current || !map) return;\n    mapRef.current = map;\n    map.setOptions({\n      draggableCursor: 'crosshair',\n      draggingCursor: 'grab'\n    });\n  }\n  let bounds = null;\n  if (props.google && props.google.maps) {\n    bounds = new props.google.maps.LatLngBounds();\n    if (markers.length > 0) {\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n    } else {\n      bounds.extend(center);\n    }\n  }\n  const OPTIONS = {\n    minZoom: 15,\n    maxZoom: 15\n  };\n\n  // Show loading if Google Maps is not ready\n  if (!props.google || !props.google.maps) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"map-container\",\n      style: {\n        height: 500,\n        width: '100%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading Google Maps...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"map-container\",\n    style: {\n      height: 500,\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"map-button\",\n      type: \"button\",\n      onClick: () => {\n        currentLocation();\n      },\n      children: /*#__PURE__*/_jsxDEV(BiCurrentLocation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Map, {\n      options: OPTIONS,\n      cursor: \"pointer\",\n      onClick: onClick,\n      maxZoom: 16,\n      minZoom: 2,\n      google: props.google,\n      initialCenter: defaultCenter,\n      center: center,\n      onReady: handleMapReady,\n      bounds: focus && bounds && markers.length > 0 ? bounds : undefined,\n      className: \"clickable\",\n      children: [validTriangleCoords.length > 0 && validTriangleCoords.map((item, idx) => {\n        var _props$google;\n        return /*#__PURE__*/_jsxDEV(Marker, {\n          onClick: e => onFinish(e),\n          position: item,\n          icon: {\n            url: 'https://upload.wikimedia.org/wikipedia/commons/9/94/Circle-image.svg',\n            scaledSize: (_props$google = props.google) !== null && _props$google !== void 0 && _props$google.maps ? new props.google.maps.Size(10, 10) : undefined\n          },\n          className: \"marker\"\n        }, idx, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this);\n      }), validTriangleCoords.length > 0 && (!(polygon !== null && polygon !== void 0 && polygon.length) ? /*#__PURE__*/_jsxDEV(Polyline, {\n        path: validTriangleCoords,\n        strokeColor: \"black\",\n        strokeOpacity: 0.8,\n        strokeWeight: 3,\n        fillColor: \"black\",\n        fillOpacity: 0.35\n      }, validTriangleCoords.length, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Polygon, {\n        path: validTriangleCoords,\n        strokeColor: \"black\",\n        strokeOpacity: 0.8,\n        strokeWeight: 3,\n        fillColor: \"black\",\n        fillOpacity: 0.35\n      }, polygon.length, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(DrawingManager, \"BfniUxSQeY+2FD8wUnR+UxnANmc=\");\n_c = DrawingManager;\nexport default _c2 = withGoogleMaps(DrawingManager, {\n  LoadingContainer: () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading Google Maps...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 27\n  }, this),\n  ErrorContainer: ({\n    error\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center',\n      color: 'red'\n    },\n    children: [\"Error loading Google Maps: \", (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error']\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this)\n});\nvar _c, _c2;\n$RefreshReg$(_c, \"DrawingManager\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["Map", "<PERSON><PERSON>", "Polygon", "Polyline", "React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "BiCurrentLocation", "getMapApiKey", "getDefaultCenter", "withGoogleMaps", "jsxDEV", "_jsxDEV", "mapApiKey", "defaultCenter", "DrawingManager", "props", "_s", "rawCoords", "Array", "isArray", "triangleCoords", "validTriangleCoords", "map", "coord", "lat", "undefined", "lng", "Number", "filter", "isNaN", "markers", "setMarkers", "center", "setCenter", "polygon", "setPolygon", "finish", "<PERSON><PERSON><PERSON><PERSON>", "length", "focus", "setFocus", "mapRef", "isMountedRef", "current", "setMerge", "onClick", "t", "cord", "latLng", "settriangleCoords", "prev", "onFinish", "e", "_validTriangleCoords$", "_e$position", "position", "setLocation", "currentLocation", "navigator", "geolocation", "getCurrentPosition", "coords", "latitude", "longitude", "error", "console", "handleMapReady", "_", "setOptions", "draggableCursor", "draggingCursor", "bounds", "google", "maps", "LatLngBounds", "i", "extend", "OPTIONS", "minZoom", "max<PERSON><PERSON>", "className", "style", "height", "width", "display", "alignItems", "justifyContent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "options", "cursor", "initialCenter", "onReady", "item", "idx", "_props$google", "icon", "url", "scaledSize", "Size", "path", "strokeColor", "strokeOpacity", "strokeWeight", "fillColor", "fillOpacity", "_c", "_c2", "LoadingContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "padding", "textAlign", "color", "message", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/drawing-map.js"], "sourcesContent": ["import {\n  Map,\n  Marker,\n  Polygon,\n  Polyline,\n} from 'google-maps-react';\nimport React from 'react';\nimport { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { BiCurrentLocation } from 'react-icons/bi';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport getDefaultCenter from 'helpers/getDefaultCenter';\nimport { withGoogleMaps } from './GoogleMapsWrapper';\n\nconst mapApiKey = getMapApiKey();\nconst defaultCenter = getDefaultCenter();\n\nconst DrawingManager = (props) => {\n  // Memoize the raw coordinates to prevent infinite re-renders\n  const rawCoords = useMemo(() => {\n    return Array.isArray(props.triangleCoords) ? props.triangleCoords : [];\n  }, [props.triangleCoords]);\n\n  // Memoize the coordinate transformation to prevent infinite re-renders\n  const validTriangleCoords = useMemo(() => {\n    return rawCoords.map(coord => {\n      if (coord && typeof coord === 'object' && coord.lat !== undefined && coord.lng !== undefined) {\n        return {\n          lat: Number(coord.lat),\n          lng: Number(coord.lng)\n        };\n      }\n      return coord;\n    }).filter(coord => coord && typeof coord === 'object' && !isNaN(coord.lat) && !isNaN(coord.lng));\n  }, [rawCoords]);\n\n\n\n  const [markers, setMarkers] = useState(validTriangleCoords);\n  const [center, setCenter] = useState(defaultCenter);\n  const [polygon, setPolygon] = useState(validTriangleCoords);\n  const [finish, setFinish] = useState(validTriangleCoords.length > 0);\n  const [focus, setFocus] = useState(false);\n  const mapRef = useRef(null);\n  const isMountedRef = useRef(true);\n\n\n\n  // Track component mounting state\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  // Update markers when triangleCoords prop changes\n  useEffect(() => {\n    if (isMountedRef.current) {\n      setMarkers(validTriangleCoords);\n    }\n  }, [validTriangleCoords]);\n\n  useEffect(() => {\n    if (isMountedRef.current && props.setMerge) {\n      props.setMerge(finish);\n    }\n  }, [finish, props]);\n\n  const onClick = (t, map, cord) => {\n    if (!isMountedRef.current) return;\n\n    setFocus(false);\n    const { latLng } = cord;\n    const lat = latLng.lat();\n    const lng = latLng.lng();\n    if (finish) {\n      setPolygon([]);\n      props.settriangleCoords([{ lat, lng }]);\n      setCenter({ lat, lng });\n      setFinish(false);\n    } else {\n      props.settriangleCoords((prev) => [...prev, { lat, lng }]);\n    }\n  };\n\n  const onFinish = (e) => {\n    if (!isMountedRef.current) return;\n\n    setFinish(validTriangleCoords.length > 0);\n    if (\n      validTriangleCoords[0]?.lat === e.position?.lat &&\n      validTriangleCoords.length > 1\n    ) {\n      setPolygon(validTriangleCoords);\n      props.setLocation && props.setLocation(validTriangleCoords);\n      setFinish(true);\n      setFocus(true);\n    }\n  };\n\n  const currentLocation = () => {\n    if (!isMountedRef.current) return;\n\n    navigator.geolocation.getCurrentPosition(\n      function (position) {\n        if (isMountedRef.current) {\n          setCenter({\n            lat: position.coords.latitude,\n            lng: position.coords.longitude,\n          });\n        }\n      },\n      function (error) {\n        console.error('Error getting current location:', error);\n      }\n    );\n  };\n\n  useEffect(() => {\n    setFocus(true);\n  }, []);\n\n  function handleMapReady(_, map) {\n    if (!isMountedRef.current || !map) return;\n\n    mapRef.current = map;\n    map.setOptions({\n      draggableCursor: 'crosshair',\n      draggingCursor: 'grab',\n    });\n  }\n\n\n\n  let bounds = null;\n  if (props.google && props.google.maps) {\n    bounds = new props.google.maps.LatLngBounds();\n    if (markers.length > 0) {\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n    } else {\n      bounds.extend(center);\n    }\n  }\n\n  const OPTIONS = {\n    minZoom: 15,\n    maxZoom: 15,\n  };\n\n  // Show loading if Google Maps is not ready\n  if (!props.google || !props.google.maps) {\n    return (\n      <div className='map-container' style={{ height: 500, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <div>Loading Google Maps...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className='map-container' style={{ height: 500, width: '100%' }}>\n      <button\n        className='map-button'\n        type='button'\n        onClick={() => {\n          currentLocation();\n        }}\n      >\n        <BiCurrentLocation />\n      </button>\n      <Map\n        options={OPTIONS}\n        cursor='pointer'\n        onClick={onClick}\n        maxZoom={16}\n        minZoom={2}\n        google={props.google}\n        initialCenter={defaultCenter}\n        center={center}\n        onReady={handleMapReady}\n        bounds={focus && bounds && markers.length > 0 ? bounds : undefined}\n        className='clickable'\n      >\n        {validTriangleCoords.length > 0 && validTriangleCoords.map((item, idx) => (\n          <Marker\n            onClick={(e) => onFinish(e)}\n            key={idx}\n            position={item}\n            icon={{\n              url: 'https://upload.wikimedia.org/wikipedia/commons/9/94/Circle-image.svg',\n              scaledSize: props.google?.maps ? new props.google.maps.Size(10, 10) : undefined,\n            }}\n            className='marker'\n          />\n        ))}\n\n        {validTriangleCoords.length > 0 && (\n          !polygon?.length ? (\n            <Polyline\n              key={validTriangleCoords.length}\n              path={validTriangleCoords}\n              strokeColor='black'\n              strokeOpacity={0.8}\n              strokeWeight={3}\n              fillColor='black'\n              fillOpacity={0.35}\n\n            />\n          ) : (\n            <Polygon\n              key={polygon.length}\n              path={validTriangleCoords}\n              strokeColor='black'\n              strokeOpacity={0.8}\n              strokeWeight={3}\n              fillColor='black'\n              fillOpacity={0.35}\n\n            />\n          )\n        )}\n      </Map>\n    </div>\n  );\n};\n\nexport default withGoogleMaps(DrawingManager, {\n  LoadingContainer: () => <div>Loading Google Maps...</div>,\n  ErrorContainer: ({ error }) => (\n    <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>\n      Error loading Google Maps: {error?.message || 'Unknown error'}\n    </div>\n  ),\n});\n"], "mappings": ";;AAAA,SACEA,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,QAAQ,QACH,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACzE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,SAASC,cAAc,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGL,YAAY,CAAC,CAAC;AAChC,MAAMM,aAAa,GAAGL,gBAAgB,CAAC,CAAC;AAExC,MAAMM,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAChC;EACA,MAAMC,SAAS,GAAGZ,OAAO,CAAC,MAAM;IAC9B,OAAOa,KAAK,CAACC,OAAO,CAACJ,KAAK,CAACK,cAAc,CAAC,GAAGL,KAAK,CAACK,cAAc,GAAG,EAAE;EACxE,CAAC,EAAE,CAACL,KAAK,CAACK,cAAc,CAAC,CAAC;;EAE1B;EACA,MAAMC,mBAAmB,GAAGhB,OAAO,CAAC,MAAM;IACxC,OAAOY,SAAS,CAACK,GAAG,CAACC,KAAK,IAAI;MAC5B,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,GAAG,KAAKC,SAAS,IAAIF,KAAK,CAACG,GAAG,KAAKD,SAAS,EAAE;QAC5F,OAAO;UACLD,GAAG,EAAEG,MAAM,CAACJ,KAAK,CAACC,GAAG,CAAC;UACtBE,GAAG,EAAEC,MAAM,CAACJ,KAAK,CAACG,GAAG;QACvB,CAAC;MACH;MACA,OAAOH,KAAK;IACd,CAAC,CAAC,CAACK,MAAM,CAACL,KAAK,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACM,KAAK,CAACN,KAAK,CAACC,GAAG,CAAC,IAAI,CAACK,KAAK,CAACN,KAAK,CAACG,GAAG,CAAC,CAAC;EAClG,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAIf,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAACoB,mBAAmB,CAAC;EAC3D,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAACY,aAAa,CAAC;EACnD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAACoB,mBAAmB,CAAC;EAC3D,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAACoB,mBAAmB,CAACiB,MAAM,GAAG,CAAC,CAAC;EACpE,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAMwC,MAAM,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMuC,YAAY,GAAGvC,MAAM,CAAC,IAAI,CAAC;;EAIjC;EACAD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwC,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzC,SAAS,CAAC,MAAM;IACd,IAAIwC,YAAY,CAACC,OAAO,EAAE;MACxBZ,UAAU,CAACV,mBAAmB,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzBnB,SAAS,CAAC,MAAM;IACd,IAAIwC,YAAY,CAACC,OAAO,IAAI5B,KAAK,CAAC6B,QAAQ,EAAE;MAC1C7B,KAAK,CAAC6B,QAAQ,CAACR,MAAM,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,MAAM,EAAErB,KAAK,CAAC,CAAC;EAEnB,MAAM8B,OAAO,GAAGA,CAACC,CAAC,EAAExB,GAAG,EAAEyB,IAAI,KAAK;IAChC,IAAI,CAACL,YAAY,CAACC,OAAO,EAAE;IAE3BH,QAAQ,CAAC,KAAK,CAAC;IACf,MAAM;MAAEQ;IAAO,CAAC,GAAGD,IAAI;IACvB,MAAMvB,GAAG,GAAGwB,MAAM,CAACxB,GAAG,CAAC,CAAC;IACxB,MAAME,GAAG,GAAGsB,MAAM,CAACtB,GAAG,CAAC,CAAC;IACxB,IAAIU,MAAM,EAAE;MACVD,UAAU,CAAC,EAAE,CAAC;MACdpB,KAAK,CAACkC,iBAAiB,CAAC,CAAC;QAAEzB,GAAG;QAAEE;MAAI,CAAC,CAAC,CAAC;MACvCO,SAAS,CAAC;QAAET,GAAG;QAAEE;MAAI,CAAC,CAAC;MACvBW,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,MAAM;MACLtB,KAAK,CAACkC,iBAAiB,CAAEC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE;QAAE1B,GAAG;QAAEE;MAAI,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMyB,QAAQ,GAAIC,CAAC,IAAK;IAAA,IAAAC,qBAAA,EAAAC,WAAA;IACtB,IAAI,CAACZ,YAAY,CAACC,OAAO,EAAE;IAE3BN,SAAS,CAAChB,mBAAmB,CAACiB,MAAM,GAAG,CAAC,CAAC;IACzC,IACE,EAAAe,qBAAA,GAAAhC,mBAAmB,CAAC,CAAC,CAAC,cAAAgC,qBAAA,uBAAtBA,qBAAA,CAAwB7B,GAAG,QAAA8B,WAAA,GAAKF,CAAC,CAACG,QAAQ,cAAAD,WAAA,uBAAVA,WAAA,CAAY9B,GAAG,KAC/CH,mBAAmB,CAACiB,MAAM,GAAG,CAAC,EAC9B;MACAH,UAAU,CAACd,mBAAmB,CAAC;MAC/BN,KAAK,CAACyC,WAAW,IAAIzC,KAAK,CAACyC,WAAW,CAACnC,mBAAmB,CAAC;MAC3DgB,SAAS,CAAC,IAAI,CAAC;MACfG,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACf,YAAY,CAACC,OAAO,EAAE;IAE3Be,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACtC,UAAUL,QAAQ,EAAE;MAClB,IAAIb,YAAY,CAACC,OAAO,EAAE;QACxBV,SAAS,CAAC;UACRT,GAAG,EAAE+B,QAAQ,CAACM,MAAM,CAACC,QAAQ;UAC7BpC,GAAG,EAAE6B,QAAQ,CAACM,MAAM,CAACE;QACvB,CAAC,CAAC;MACJ;IACF,CAAC,EACD,UAAUC,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CACF,CAAC;EACH,CAAC;EAED9D,SAAS,CAAC,MAAM;IACdsC,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,SAAS0B,cAAcA,CAACC,CAAC,EAAE7C,GAAG,EAAE;IAC9B,IAAI,CAACoB,YAAY,CAACC,OAAO,IAAI,CAACrB,GAAG,EAAE;IAEnCmB,MAAM,CAACE,OAAO,GAAGrB,GAAG;IACpBA,GAAG,CAAC8C,UAAU,CAAC;MACbC,eAAe,EAAE,WAAW;MAC5BC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;EAIA,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIxD,KAAK,CAACyD,MAAM,IAAIzD,KAAK,CAACyD,MAAM,CAACC,IAAI,EAAE;IACrCF,MAAM,GAAG,IAAIxD,KAAK,CAACyD,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAAC;IAC7C,IAAI5C,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;MACtB,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7C,OAAO,CAACQ,MAAM,EAAEqC,CAAC,EAAE,EAAE;QACvCJ,MAAM,CAACK,MAAM,CAAC9C,OAAO,CAAC6C,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC,MAAM;MACLJ,MAAM,CAACK,MAAM,CAAC5C,MAAM,CAAC;IACvB;EACF;EAEA,MAAM6C,OAAO,GAAG;IACdC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC;;EAED;EACA,IAAI,CAAChE,KAAK,CAACyD,MAAM,IAAI,CAACzD,KAAK,CAACyD,MAAM,CAACC,IAAI,EAAE;IACvC,oBACE9D,OAAA;MAAKqE,SAAS,EAAC,eAAe;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,eACpI5E,OAAA;QAAA4E,QAAA,EAAK;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAKqE,SAAS,EAAC,eAAe;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAI,QAAA,gBACnE5E,OAAA;MACEqE,SAAS,EAAC,YAAY;MACtBY,IAAI,EAAC,QAAQ;MACb/C,OAAO,EAAEA,CAAA,KAAM;QACbY,eAAe,CAAC,CAAC;MACnB,CAAE;MAAA8B,QAAA,eAEF5E,OAAA,CAACL,iBAAiB;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACThF,OAAA,CAACf,GAAG;MACFiG,OAAO,EAAEhB,OAAQ;MACjBiB,MAAM,EAAC,SAAS;MAChBjD,OAAO,EAAEA,OAAQ;MACjBkC,OAAO,EAAE,EAAG;MACZD,OAAO,EAAE,CAAE;MACXN,MAAM,EAAEzD,KAAK,CAACyD,MAAO;MACrBuB,aAAa,EAAElF,aAAc;MAC7BmB,MAAM,EAAEA,MAAO;MACfgE,OAAO,EAAE9B,cAAe;MACxBK,MAAM,EAAEhC,KAAK,IAAIgC,MAAM,IAAIzC,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGiC,MAAM,GAAG9C,SAAU;MACnEuD,SAAS,EAAC,WAAW;MAAAO,QAAA,GAEpBlE,mBAAmB,CAACiB,MAAM,GAAG,CAAC,IAAIjB,mBAAmB,CAACC,GAAG,CAAC,CAAC2E,IAAI,EAAEC,GAAG;QAAA,IAAAC,aAAA;QAAA,oBACnExF,OAAA,CAACd,MAAM;UACLgD,OAAO,EAAGO,CAAC,IAAKD,QAAQ,CAACC,CAAC,CAAE;UAE5BG,QAAQ,EAAE0C,IAAK;UACfG,IAAI,EAAE;YACJC,GAAG,EAAE,sEAAsE;YAC3EC,UAAU,EAAE,CAAAH,aAAA,GAAApF,KAAK,CAACyD,MAAM,cAAA2B,aAAA,eAAZA,aAAA,CAAc1B,IAAI,GAAG,IAAI1D,KAAK,CAACyD,MAAM,CAACC,IAAI,CAAC8B,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG9E;UACxE,CAAE;UACFuD,SAAS,EAAC;QAAQ,GANbkB,GAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOT,CAAC;MAAA,CACH,CAAC,EAEDtE,mBAAmB,CAACiB,MAAM,GAAG,CAAC,KAC7B,EAACJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,MAAM,iBACd3B,OAAA,CAACZ,QAAQ;QAEPyG,IAAI,EAAEnF,mBAAoB;QAC1BoF,WAAW,EAAC,OAAO;QACnBC,aAAa,EAAE,GAAI;QACnBC,YAAY,EAAE,CAAE;QAChBC,SAAS,EAAC,OAAO;QACjBC,WAAW,EAAE;MAAK,GANbxF,mBAAmB,CAACiB,MAAM;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQhC,CAAC,gBAEFhF,OAAA,CAACb,OAAO;QAEN0G,IAAI,EAAEnF,mBAAoB;QAC1BoF,WAAW,EAAC,OAAO;QACnBC,aAAa,EAAE,GAAI;QACnBC,YAAY,EAAE,CAAE;QAChBC,SAAS,EAAC,OAAO;QACjBC,WAAW,EAAE;MAAK,GANb3E,OAAO,CAACI,MAAM;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQpB,CACF,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAhNIF,cAAc;AAAAgG,EAAA,GAAdhG,cAAc;AAkNpB,eAAAiG,GAAA,GAAetG,cAAc,CAACK,cAAc,EAAE;EAC5CkG,gBAAgB,EAAEA,CAAA,kBAAMrG,OAAA;IAAA4E,QAAA,EAAK;EAAsB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzDsB,cAAc,EAAEA,CAAC;IAAEjD;EAAM,CAAC,kBACxBrD,OAAA;IAAKsE,KAAK,EAAE;MAAEiC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAM,CAAE;IAAA7B,QAAA,GAAC,6BACvC,EAAC,CAAAvB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqD,OAAO,KAAI,eAAe;EAAA;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D;AAET,CAAC,CAAC;AAAC,IAAAmB,EAAA,EAAAC,GAAA;AAAAO,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}