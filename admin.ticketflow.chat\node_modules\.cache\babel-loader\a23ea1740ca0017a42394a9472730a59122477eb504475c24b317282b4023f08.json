{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\user\\\\deliveryman-zone.js\",\n  _s = $RefreshSig$();\nimport { <PERSON><PERSON>, Card, Col, Form, Row } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport DrawingManager from 'components/drawing-map';\nimport React, { useEffect, useState, useRef, useCallback } from 'react';\nimport MapGif from 'assets/video/map.gif';\nimport { toast } from 'react-toastify';\nimport userService from 'services/user';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeliverymanZone = ({\n  user_id\n}) => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [form] = Form.useForm();\n  const [triangleCoords, setTriangleCoords] = useState([]);\n  const [merge, setMerge] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const isMountedRef = useRef(true);\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  const fetchDeliverymanZone = id => {\n    if (!isMountedRef.current) return;\n    setLoading(true);\n    userService.showDeliverymanZone(id).then(res => {\n      if (isMountedRef.current) {\n        var _res$data;\n        setTriangleCoords(res.data !== undefined ? (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.map(item => ({\n          lat: item === null || item === void 0 ? void 0 : item[0],\n          lng: item === null || item === void 0 ? void 0 : item[1]\n        })) : []);\n      }\n    }).catch(err => {\n      if (isMountedRef.current) {\n        var _err$response;\n        if ((err === null || err === void 0 ? void 0 : (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 404) {\n          // 404 é esperado quando não há zona de entrega configurada\n          console.info('No delivery zone found for user:', id);\n          setTriangleCoords([]);\n        } else {\n          console.error('Error fetching delivery zone:', err);\n          toast.error(t('error.fetching.delivery.zone'));\n        }\n      }\n    }).finally(() => {\n      if (isMountedRef.current) {\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    if (user_id) {\n      fetchDeliverymanZone(user_id);\n    }\n  }, [user_id, fetchDeliverymanZone]);\n  const onFinish = () => {\n    if (!isMountedRef.current) return;\n    if (!user_id) {\n      toast.error(t('no.user.id'));\n      return;\n    }\n    if (triangleCoords.length < 3) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    if (!merge) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    const body = {\n      user_id,\n      address: triangleCoords.map(item => ({\n        0: item.lat,\n        1: item.lng\n      }))\n    };\n    setLoadingBtn(true);\n    userService.createAndUpdateDeliverymanZone(body).then(() => {\n      if (isMountedRef.current) {\n        toast.success(t('successfully.updated'));\n      }\n    }).finally(() => {\n      if (isMountedRef.current) {\n        setLoadingBtn(false);\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    loading: loading,\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      form: form,\n      onFinish: onFinish,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 12,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: MapGif,\n            alt: t('map.gif'),\n            style: {\n              object: 'contain'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(DrawingManager, {\n            triangleCoords: triangleCoords,\n            settriangleCoords: setTriangleCoords,\n            setMerge: setMerge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        loading: loadingBtn,\n        className: \"mt-4\",\n        children: t('save')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(DeliverymanZone, \"fCJ24DSO+kEC3ulQMesaC92fSpY=\", false, function () {\n  return [useTranslation, Form.useForm];\n});\n_c = DeliverymanZone;\nexport default DeliverymanZone;\nvar _c;\n$RefreshReg$(_c, \"DeliverymanZone\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Card", "Col", "Form", "Row", "useTranslation", "DrawingManager", "React", "useEffect", "useState", "useRef", "useCallback", "MapGif", "toast", "userService", "jsxDEV", "_jsxDEV", "DeliverymanZone", "user_id", "_s", "t", "form", "useForm", "triangleCoords", "setTriangleCoords", "merge", "setMerge", "loading", "setLoading", "loadingBtn", "setLoadingBtn", "isMountedRef", "current", "fetchDeliverymanZone", "id", "showDeliverymanZone", "then", "res", "_res$data", "data", "undefined", "map", "item", "lat", "lng", "catch", "err", "_err$response", "response", "status", "console", "info", "error", "finally", "onFinish", "length", "warning", "body", "address", "createAndUpdateDeliverymanZone", "success", "children", "layout", "gutter", "span", "src", "alt", "style", "object", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "settriangleCoords", "type", "htmlType", "className", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/user/deliveryman-zone.js"], "sourcesContent": ["import { <PERSON><PERSON>, Card, Col, Form, Row } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport DrawingManager from 'components/drawing-map';\nimport React, { useEffect, useState, useRef, useCallback } from 'react';\nimport MapGif from 'assets/video/map.gif';\nimport { toast } from 'react-toastify';\nimport userService from 'services/user';\n\nconst DeliverymanZone = ({ user_id }) => {\n  const { t } = useTranslation();\n  const [form] = Form.useForm();\n\n  const [triangleCoords, setTriangleCoords] = useState([]);\n  const [merge, setMerge] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const isMountedRef = useRef(true);\n\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n\n  const fetchDeliverymanZone = (id) => {\n    if (!isMountedRef.current) return;\n\n    setLoading(true);\n    userService\n      .showDeliverymanZone(id)\n      .then((res) => {\n        if (isMountedRef.current) {\n          setTriangleCoords(\n            res.data !== undefined\n              ? res.data?.map((item) => ({\n                  lat: item?.[0],\n                  lng: item?.[1],\n                }))\n              : [],\n          );\n        }\n      })\n      .catch((err) => {\n        if (isMountedRef.current) {\n          if (err?.response?.status === 404) {\n            // 404 é esperado quando não há zona de entrega configurada\n            console.info('No delivery zone found for user:', id);\n            setTriangleCoords([]);\n          } else {\n            console.error('Error fetching delivery zone:', err);\n            toast.error(t('error.fetching.delivery.zone'));\n          }\n        }\n      })\n      .finally(() => {\n        if (isMountedRef.current) {\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    if (user_id) {\n      fetchDeliverymanZone(user_id);\n    }\n  }, [user_id, fetchDeliverymanZone]);\n\n  const onFinish = () => {\n    if (!isMountedRef.current) return;\n\n    if (!user_id) {\n      toast.error(t('no.user.id'));\n      return;\n    }\n    if (triangleCoords.length < 3) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n\n    if (!merge) {\n      toast.warning(t('place.selected.map'));\n      return;\n    }\n    const body = {\n      user_id,\n      address: triangleCoords.map((item) => ({\n        0: item.lat,\n        1: item.lng,\n      })),\n    };\n    setLoadingBtn(true);\n    userService\n      .createAndUpdateDeliverymanZone(body)\n      .then(() => {\n        if (isMountedRef.current) {\n          toast.success(t('successfully.updated'));\n        }\n      })\n      .finally(() => {\n        if (isMountedRef.current) {\n          setLoadingBtn(false);\n        }\n      });\n  };\n\n  return (\n    <Card loading={loading}>\n      <Form layout='vertical' form={form} onFinish={onFinish}>\n        <Row gutter={12}>\n          <Col span={12}>\n            <img\n              src={MapGif}\n              alt={t('map.gif')}\n              style={{ object: 'contain' }}\n            />\n          </Col>\n          <Col span={24}>\n            <DrawingManager\n              triangleCoords={triangleCoords}\n              settriangleCoords={setTriangleCoords}\n              setMerge={setMerge}\n            />\n          </Col>\n        </Row>\n        <Button\n          type='primary'\n          htmlType='submit'\n          loading={loadingBtn}\n          className='mt-4'\n        >\n          {t('save')}\n        </Button>\n      </Form>\n    </Card>\n  );\n};\n\nexport default DeliverymanZone;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AACnD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAE,CAAC,GAAGf,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACgB,IAAI,CAAC,GAAGlB,IAAI,CAACmB,OAAO,CAAC,CAAC;EAE7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMsB,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAEjCF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXuB,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAIC,EAAE,IAAK;IACnC,IAAI,CAACH,YAAY,CAACC,OAAO,EAAE;IAE3BJ,UAAU,CAAC,IAAI,CAAC;IAChBd,WAAW,CACRqB,mBAAmB,CAACD,EAAE,CAAC,CACvBE,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIN,YAAY,CAACC,OAAO,EAAE;QAAA,IAAAM,SAAA;QACxBd,iBAAiB,CACfa,GAAG,CAACE,IAAI,KAAKC,SAAS,IAAAF,SAAA,GAClBD,GAAG,CAACE,IAAI,cAAAD,SAAA,uBAARA,SAAA,CAAUG,GAAG,CAAEC,IAAI,KAAM;UACvBC,GAAG,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC,CAAC;UACdE,GAAG,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC;QACf,CAAC,CAAC,CAAC,GACH,EACN,CAAC;MACH;IACF,CAAC,CAAC,CACDG,KAAK,CAAEC,GAAG,IAAK;MACd,IAAIf,YAAY,CAACC,OAAO,EAAE;QAAA,IAAAe,aAAA;QACxB,IAAI,CAAAD,GAAG,aAAHA,GAAG,wBAAAC,aAAA,GAAHD,GAAG,CAAEE,QAAQ,cAAAD,aAAA,uBAAbA,aAAA,CAAeE,MAAM,MAAK,GAAG,EAAE;UACjC;UACAC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEjB,EAAE,CAAC;UACpDV,iBAAiB,CAAC,EAAE,CAAC;QACvB,CAAC,MAAM;UACL0B,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEN,GAAG,CAAC;UACnDjC,KAAK,CAACuC,KAAK,CAAChC,CAAC,CAAC,8BAA8B,CAAC,CAAC;QAChD;MACF;IACF,CAAC,CAAC,CACDiC,OAAO,CAAC,MAAM;MACb,IAAItB,YAAY,CAACC,OAAO,EAAE;QACxBJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACd,IAAIU,OAAO,EAAE;MACXe,oBAAoB,CAACf,OAAO,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,OAAO,EAAEe,oBAAoB,CAAC,CAAC;EAEnC,MAAMqB,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACvB,YAAY,CAACC,OAAO,EAAE;IAE3B,IAAI,CAACd,OAAO,EAAE;MACZL,KAAK,CAACuC,KAAK,CAAChC,CAAC,CAAC,YAAY,CAAC,CAAC;MAC5B;IACF;IACA,IAAIG,cAAc,CAACgC,MAAM,GAAG,CAAC,EAAE;MAC7B1C,KAAK,CAAC2C,OAAO,CAACpC,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtC;IACF;IAEA,IAAI,CAACK,KAAK,EAAE;MACVZ,KAAK,CAAC2C,OAAO,CAACpC,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtC;IACF;IACA,MAAMqC,IAAI,GAAG;MACXvC,OAAO;MACPwC,OAAO,EAAEnC,cAAc,CAACkB,GAAG,CAAEC,IAAI,KAAM;QACrC,CAAC,EAAEA,IAAI,CAACC,GAAG;QACX,CAAC,EAAED,IAAI,CAACE;MACV,CAAC,CAAC;IACJ,CAAC;IACDd,aAAa,CAAC,IAAI,CAAC;IACnBhB,WAAW,CACR6C,8BAA8B,CAACF,IAAI,CAAC,CACpCrB,IAAI,CAAC,MAAM;MACV,IAAIL,YAAY,CAACC,OAAO,EAAE;QACxBnB,KAAK,CAAC+C,OAAO,CAACxC,CAAC,CAAC,sBAAsB,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC,CACDiC,OAAO,CAAC,MAAM;MACb,IAAItB,YAAY,CAACC,OAAO,EAAE;QACxBF,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;EACN,CAAC;EAED,oBACEd,OAAA,CAACf,IAAI;IAAC0B,OAAO,EAAEA,OAAQ;IAAAkC,QAAA,eACrB7C,OAAA,CAACb,IAAI;MAAC2D,MAAM,EAAC,UAAU;MAACzC,IAAI,EAAEA,IAAK;MAACiC,QAAQ,EAAEA,QAAS;MAAAO,QAAA,gBACrD7C,OAAA,CAACZ,GAAG;QAAC2D,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACd7C,OAAA,CAACd,GAAG;UAAC8D,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZ7C,OAAA;YACEiD,GAAG,EAAErD,MAAO;YACZsD,GAAG,EAAE9C,CAAC,CAAC,SAAS,CAAE;YAClB+C,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxD,OAAA,CAACd,GAAG;UAAC8D,IAAI,EAAE,EAAG;UAAAH,QAAA,eACZ7C,OAAA,CAACV,cAAc;YACbiB,cAAc,EAAEA,cAAe;YAC/BkD,iBAAiB,EAAEjD,iBAAkB;YACrCE,QAAQ,EAAEA;UAAS;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxD,OAAA,CAAChB,MAAM;QACL0E,IAAI,EAAC,SAAS;QACdC,QAAQ,EAAC,QAAQ;QACjBhD,OAAO,EAAEE,UAAW;QACpB+C,SAAS,EAAC,MAAM;QAAAf,QAAA,EAEfzC,CAAC,CAAC,MAAM;MAAC;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACrD,EAAA,CA/HIF,eAAe;EAAA,QACLZ,cAAc,EACbF,IAAI,CAACmB,OAAO;AAAA;AAAAuD,EAAA,GAFvB5D,eAAe;AAiIrB,eAAeA,eAAe;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}