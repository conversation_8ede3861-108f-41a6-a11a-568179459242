import { <PERSON><PERSON>, Card, Col, Form, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import DrawingManager from 'components/drawing-map';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import MapGif from 'assets/video/map.gif';
import { toast } from 'react-toastify';
import userService from 'services/user';

const DeliverymanZone = ({ user_id }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const [triangleCoords, setTriangleCoords] = useState([]);
  const [merge, setMerge] = useState(null);
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const fetchDeliverymanZone = useCallback((id) => {
    if (!isMountedRef.current) return;

    setLoading(true);
    userService
      .showDeliverymanZone(id)
      .then((res) => {
        if (isMountedRef.current) {
          setTriangleCoords(
            res.data !== undefined
              ? res.data?.map((item) => ({
                  lat: item?.[0],
                  lng: item?.[1],
                }))
              : [],
          );
        }
      })
      .catch((err) => {
        if (isMountedRef.current) {
          if (err?.response?.status === 404) {
            // 404 é esperado quando não há zona de entrega configurada
            console.info('No delivery zone found for user:', id);
            setTriangleCoords([]);
          } else {
            console.error('Error fetching delivery zone:', err);
            toast.error(t('error.fetching.delivery.zone'));
          }
        }
      })
      .finally(() => {
        if (isMountedRef.current) {
          setLoading(false);
        }
      });
  }, [t]);

  useEffect(() => {
    if (user_id) {
      fetchDeliverymanZone(user_id);
    }
  }, [user_id, fetchDeliverymanZone]);

  const onFinish = () => {
    if (!isMountedRef.current) return;

    if (!user_id) {
      toast.error(t('no.user.id'));
      return;
    }
    if (triangleCoords.length < 3) {
      toast.warning(t('place.selected.map'));
      return;
    }

    if (!merge) {
      toast.warning(t('place.selected.map'));
      return;
    }
    const body = {
      user_id,
      address: triangleCoords.map((item) => ({
        0: item.lat,
        1: item.lng,
      })),
    };
    setLoadingBtn(true);
    userService
      .createAndUpdateDeliverymanZone(body)
      .then(() => {
        if (isMountedRef.current) {
          toast.success(t('successfully.updated'));
        }
      })
      .finally(() => {
        if (isMountedRef.current) {
          setLoadingBtn(false);
        }
      });
  };

  return (
    <Card loading={loading}>
      <Form layout='vertical' form={form} onFinish={onFinish}>
        <Row gutter={12}>
          <Col span={12}>
            <img
              src={MapGif}
              alt={t('map.gif')}
              style={{ object: 'contain' }}
            />
          </Col>
          <Col span={24}>
            <DrawingManager
              triangleCoords={triangleCoords}
              settriangleCoords={setTriangleCoords}
              setMerge={setMerge}
            />
          </Col>
        </Row>
        <Button
          type='primary'
          htmlType='submit'
          loading={loadingBtn}
          className='mt-4'
        >
          {t('save')}
        </Button>
      </Form>
    </Card>
  );
};

export default DeliverymanZone;
