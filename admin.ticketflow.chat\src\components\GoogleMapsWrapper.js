import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import getMapApiKey from 'helpers/getMapApiKey';

const GoogleMapsContext = createContext(null);

// Singleton para evitar múltiplos carregamentos da API
let googleMapsInstance = null;
let isLoading = false;
let loadPromise = null;

// Modern async Google Maps loader
const loadGoogleMapsAPI = async (apiKey, libraries = ['places']) => {
  if (googleMapsInstance) {
    return googleMapsInstance;
  }

  if (isLoading) {
    return loadPromise;
  }

  isLoading = true;
  loadPromise = new Promise((resolve, reject) => {
    // Check if Google Maps is already loaded
    if (window.google && window.google.maps) {
      googleMapsInstance = window.google;
      isLoading = false;
      resolve(googleMapsInstance);
      return;
    }

    // Create script element with async loading
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;

    // Generate unique callback name
    const callbackName = `googleMapsCallback_${Date.now()}`;

    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=${libraries.join(',')}&callback=${callbackName}&loading=async`;

    // Set up callback
    window[callbackName] = () => {
      googleMapsInstance = window.google;
      isLoading = false;
      delete window[callbackName]; // Clean up
      resolve(googleMapsInstance);
    };

    script.onerror = () => {
      isLoading = false;
      delete window[callbackName]; // Clean up
      reject(new Error('Failed to load Google Maps API'));
    };

    document.head.appendChild(script);
  });

  return loadPromise;
};

const GoogleMapsProvider = ({ children, apiKey, libraries = ['places'] }) => {
  const [google, setGoogle] = useState(googleMapsInstance);
  const [isReady, setIsReady] = useState(!!googleMapsInstance);
  const [error, setError] = useState(null);

  const initializeGoogleMaps = useCallback(async () => {
    try {
      if (!apiKey) {
        throw new Error('Google Maps API key is required');
      }

      const googleInstance = await loadGoogleMapsAPI(apiKey, libraries);
      setGoogle(googleInstance);
      setIsReady(true);
    } catch (err) {
      console.error('Failed to load Google Maps:', err);
      setError(err);
    }
  }, [apiKey, libraries]);

  useEffect(() => {
    if (!isReady && !error) {
      initializeGoogleMaps();
    }
  }, [initializeGoogleMaps, isReady, error]);

  const contextValue = {
    google,
    isReady,
    error,
    reload: initializeGoogleMaps
  };

  return (
    <GoogleMapsContext.Provider value={contextValue}>
      {children}
    </GoogleMapsContext.Provider>
  );
};

export const useGoogleMaps = () => {
  const context = useContext(GoogleMapsContext);
  if (!context) {
    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');
  }
  return context;
};

// Modern wrapper component that replaces GoogleApiWrapper
export const withGoogleMaps = (WrappedComponent, options = {}) => {
  const WithGoogleMapsComponent = (props) => {
    const { google, isReady, error } = useGoogleMaps();

    const LoadingContainer = options.LoadingContainer || (() => <div>Loading Google Maps...</div>);
    const ErrorContainer = options.ErrorContainer || (({ error }) => (
      <div>Error loading Google Maps: {error?.message}</div>
    ));

    if (error) {
      return <ErrorContainer error={error} />;
    }

    if (!isReady || !google) {
      return <LoadingContainer />;
    }

    return <WrappedComponent {...props} google={google} loaded={isReady} />;
  };

  WithGoogleMapsComponent.displayName = `withGoogleMaps(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithGoogleMapsComponent;
};

// Main provider component with API key
const WrappedGoogleMapsProvider = ({ children }) => {
  const apiKey = getMapApiKey();

  return (
    <GoogleMapsProvider apiKey={apiKey} libraries={['places', 'marker']}>
      {children}
    </GoogleMapsProvider>
  );
};

export default WrappedGoogleMapsProvider;
