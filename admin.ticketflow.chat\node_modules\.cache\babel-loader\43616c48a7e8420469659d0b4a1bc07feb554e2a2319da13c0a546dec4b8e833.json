{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\components\\\\GoogleMapsWrapper.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport getMapApiKey from 'helpers/getMapApiKey';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoogleMapsContext = /*#__PURE__*/createContext(null);\n\n// Singleton para evitar múltiplos carregamentos da API\nlet googleMapsInstance = null;\nlet isLoading = false;\nlet loadPromise = null;\n\n// Modern async Google Maps loader\nconst loadGoogleMapsAPI = async (apiKey, libraries = ['places']) => {\n  if (googleMapsInstance) {\n    return googleMapsInstance;\n  }\n  if (isLoading) {\n    return loadPromise;\n  }\n  isLoading = true;\n  loadPromise = new Promise((resolve, reject) => {\n    // Check if Google Maps is already loaded\n    if (window.google && window.google.maps) {\n      googleMapsInstance = window.google;\n      isLoading = false;\n      resolve(googleMapsInstance);\n      return;\n    }\n\n    // Create script element with async loading\n    const script = document.createElement('script');\n    script.async = true;\n    script.defer = true;\n\n    // Generate unique callback name\n    const callbackName = `googleMapsCallback_${Date.now()}`;\n    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=${libraries.join(',')}&callback=${callbackName}&loading=async`;\n\n    // Set up callback\n    window[callbackName] = () => {\n      googleMapsInstance = window.google;\n      isLoading = false;\n      delete window[callbackName]; // Clean up\n      resolve(googleMapsInstance);\n    };\n    script.onerror = () => {\n      isLoading = false;\n      delete window[callbackName]; // Clean up\n      reject(new Error('Failed to load Google Maps API'));\n    };\n    document.head.appendChild(script);\n  });\n  return loadPromise;\n};\nconst GoogleMapsProvider = ({\n  children,\n  apiKey,\n  libraries = ['places']\n}) => {\n  _s();\n  const [google, setGoogle] = useState(googleMapsInstance);\n  const [isReady, setIsReady] = useState(!!googleMapsInstance);\n  const [error, setError] = useState(null);\n  const initializeGoogleMaps = useCallback(async () => {\n    try {\n      if (!apiKey) {\n        throw new Error('Google Maps API key is required');\n      }\n      const googleInstance = await loadGoogleMapsAPI(apiKey, libraries);\n      setGoogle(googleInstance);\n      setIsReady(true);\n    } catch (err) {\n      console.error('Failed to load Google Maps:', err);\n      setError(err);\n    }\n  }, [apiKey, libraries]);\n  useEffect(() => {\n    if (!isReady && !error) {\n      initializeGoogleMaps();\n    }\n  }, [initializeGoogleMaps, isReady, error]);\n  const contextValue = {\n    google,\n    isReady,\n    error,\n    reload: initializeGoogleMaps\n  };\n  return /*#__PURE__*/_jsxDEV(GoogleMapsContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(GoogleMapsProvider, \"rgm0rrHNsRKbBecPsY5r4ZCJrWU=\");\n_c = GoogleMapsProvider;\nexport const useGoogleMaps = () => {\n  _s2();\n  const context = useContext(GoogleMapsContext);\n  if (!context) {\n    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');\n  }\n  return context;\n};\n\n// Modern wrapper component that replaces GoogleApiWrapper\n_s2(useGoogleMaps, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const withGoogleMaps = (WrappedComponent, options = {}) => {\n  var _s3 = $RefreshSig$();\n  const WithGoogleMapsComponent = props => {\n    _s3();\n    const {\n      google,\n      isReady,\n      error\n    } = useGoogleMaps();\n    const LoadingContainer = options.LoadingContainer || (() => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading Google Maps...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 65\n    }, this));\n    const ErrorContainer = options.ErrorContainer || (({\n      error\n    }) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Error loading Google Maps: \", error === null || error === void 0 ? void 0 : error.message]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this));\n    if (error) {\n      return /*#__PURE__*/_jsxDEV(ErrorContainer, {\n        error: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 14\n      }, this);\n    }\n    if (!isReady || !google) {\n      return /*#__PURE__*/_jsxDEV(LoadingContainer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(WrappedComponent, {\n      ...props,\n      google: google,\n      loaded: isReady\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 12\n    }, this);\n  };\n  _s3(WithGoogleMapsComponent, \"SJks4qPePn8JvE+c74Jat1w31T8=\", false, function () {\n    return [useGoogleMaps];\n  });\n  WithGoogleMapsComponent.displayName = `withGoogleMaps(${WrappedComponent.displayName || WrappedComponent.name})`;\n  return WithGoogleMapsComponent;\n};\n\n// Main provider component with API key\nconst WrappedGoogleMapsProvider = ({\n  children\n}) => {\n  const apiKey = getMapApiKey();\n  return /*#__PURE__*/_jsxDEV(GoogleMapsProvider, {\n    apiKey: apiKey,\n    libraries: ['places', 'marker'],\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_c2 = WrappedGoogleMapsProvider;\nexport default WrappedGoogleMapsProvider;\nvar _c, _c2;\n$RefreshReg$(_c, \"GoogleMapsProvider\");\n$RefreshReg$(_c2, \"WrappedGoogleMapsProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useCallback", "getMapApiKey", "jsxDEV", "_jsxDEV", "GoogleMapsContext", "googleMapsInstance", "isLoading", "loadPromise", "loadGoogleMapsAPI", "<PERSON><PERSON><PERSON><PERSON>", "libraries", "Promise", "resolve", "reject", "window", "google", "maps", "script", "document", "createElement", "async", "defer", "callback<PERSON><PERSON>", "Date", "now", "src", "join", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "GoogleMapsProvider", "children", "_s", "setGoogle", "isReady", "setIsReady", "error", "setError", "initializeGoogleMaps", "googleInstance", "err", "console", "contextValue", "reload", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useGoogleMaps", "_s2", "context", "withGoogleMaps", "WrappedComponent", "options", "_s3", "$RefreshSig$", "WithGoogleMapsComponent", "props", "LoadingContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "loaded", "displayName", "name", "WrappedGoogleMapsProvider", "_c2", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/components/GoogleMapsWrapper.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport getMapApiKey from 'helpers/getMapApiKey';\n\nconst GoogleMapsContext = createContext(null);\n\n// Singleton para evitar múltiplos carregamentos da API\nlet googleMapsInstance = null;\nlet isLoading = false;\nlet loadPromise = null;\n\n// Modern async Google Maps loader\nconst loadGoogleMapsAPI = async (apiKey, libraries = ['places']) => {\n  if (googleMapsInstance) {\n    return googleMapsInstance;\n  }\n\n  if (isLoading) {\n    return loadPromise;\n  }\n\n  isLoading = true;\n  loadPromise = new Promise((resolve, reject) => {\n    // Check if Google Maps is already loaded\n    if (window.google && window.google.maps) {\n      googleMapsInstance = window.google;\n      isLoading = false;\n      resolve(googleMapsInstance);\n      return;\n    }\n\n    // Create script element with async loading\n    const script = document.createElement('script');\n    script.async = true;\n    script.defer = true;\n\n    // Generate unique callback name\n    const callbackName = `googleMapsCallback_${Date.now()}`;\n\n    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=${libraries.join(',')}&callback=${callbackName}&loading=async`;\n\n    // Set up callback\n    window[callbackName] = () => {\n      googleMapsInstance = window.google;\n      isLoading = false;\n      delete window[callbackName]; // Clean up\n      resolve(googleMapsInstance);\n    };\n\n    script.onerror = () => {\n      isLoading = false;\n      delete window[callbackName]; // Clean up\n      reject(new Error('Failed to load Google Maps API'));\n    };\n\n    document.head.appendChild(script);\n  });\n\n  return loadPromise;\n};\n\nconst GoogleMapsProvider = ({ children, apiKey, libraries = ['places'] }) => {\n  const [google, setGoogle] = useState(googleMapsInstance);\n  const [isReady, setIsReady] = useState(!!googleMapsInstance);\n  const [error, setError] = useState(null);\n\n  const initializeGoogleMaps = useCallback(async () => {\n    try {\n      if (!apiKey) {\n        throw new Error('Google Maps API key is required');\n      }\n\n      const googleInstance = await loadGoogleMapsAPI(apiKey, libraries);\n      setGoogle(googleInstance);\n      setIsReady(true);\n    } catch (err) {\n      console.error('Failed to load Google Maps:', err);\n      setError(err);\n    }\n  }, [apiKey, libraries]);\n\n  useEffect(() => {\n    if (!isReady && !error) {\n      initializeGoogleMaps();\n    }\n  }, [initializeGoogleMaps, isReady, error]);\n\n  const contextValue = {\n    google,\n    isReady,\n    error,\n    reload: initializeGoogleMaps\n  };\n\n  return (\n    <GoogleMapsContext.Provider value={contextValue}>\n      {children}\n    </GoogleMapsContext.Provider>\n  );\n};\n\nexport const useGoogleMaps = () => {\n  const context = useContext(GoogleMapsContext);\n  if (!context) {\n    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');\n  }\n  return context;\n};\n\n// Modern wrapper component that replaces GoogleApiWrapper\nexport const withGoogleMaps = (WrappedComponent, options = {}) => {\n  const WithGoogleMapsComponent = (props) => {\n    const { google, isReady, error } = useGoogleMaps();\n\n    const LoadingContainer = options.LoadingContainer || (() => <div>Loading Google Maps...</div>);\n    const ErrorContainer = options.ErrorContainer || (({ error }) => (\n      <div>Error loading Google Maps: {error?.message}</div>\n    ));\n\n    if (error) {\n      return <ErrorContainer error={error} />;\n    }\n\n    if (!isReady || !google) {\n      return <LoadingContainer />;\n    }\n\n    return <WrappedComponent {...props} google={google} loaded={isReady} />;\n  };\n\n  WithGoogleMapsComponent.displayName = `withGoogleMaps(${WrappedComponent.displayName || WrappedComponent.name})`;\n\n  return WithGoogleMapsComponent;\n};\n\n// Main provider component with API key\nconst WrappedGoogleMapsProvider = ({ children }) => {\n  const apiKey = getMapApiKey();\n\n  return (\n    <GoogleMapsProvider apiKey={apiKey} libraries={['places', 'marker']}>\n      {children}\n    </GoogleMapsProvider>\n  );\n};\n\nexport default WrappedGoogleMapsProvider;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,iBAAiB,gBAAGR,aAAa,CAAC,IAAI,CAAC;;AAE7C;AACA,IAAIS,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,SAAS,GAAG,KAAK;AACrB,IAAIC,WAAW,GAAG,IAAI;;AAEtB;AACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,SAAS,GAAG,CAAC,QAAQ,CAAC,KAAK;EAClE,IAAIL,kBAAkB,EAAE;IACtB,OAAOA,kBAAkB;EAC3B;EAEA,IAAIC,SAAS,EAAE;IACb,OAAOC,WAAW;EACpB;EAEAD,SAAS,GAAG,IAAI;EAChBC,WAAW,GAAG,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC7C;IACA,IAAIC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;MACvCX,kBAAkB,GAAGS,MAAM,CAACC,MAAM;MAClCT,SAAS,GAAG,KAAK;MACjBM,OAAO,CAACP,kBAAkB,CAAC;MAC3B;IACF;;IAEA;IACA,MAAMY,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,KAAK,GAAG,IAAI;IACnBH,MAAM,CAACI,KAAK,GAAG,IAAI;;IAEnB;IACA,MAAMC,YAAY,GAAI,sBAAqBC,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;IAEvDP,MAAM,CAACQ,GAAG,GAAI,+CAA8ChB,MAAO,cAAaC,SAAS,CAACgB,IAAI,CAAC,GAAG,CAAE,aAAYJ,YAAa,gBAAe;;IAE5I;IACAR,MAAM,CAACQ,YAAY,CAAC,GAAG,MAAM;MAC3BjB,kBAAkB,GAAGS,MAAM,CAACC,MAAM;MAClCT,SAAS,GAAG,KAAK;MACjB,OAAOQ,MAAM,CAACQ,YAAY,CAAC,CAAC,CAAC;MAC7BV,OAAO,CAACP,kBAAkB,CAAC;IAC7B,CAAC;IAEDY,MAAM,CAACU,OAAO,GAAG,MAAM;MACrBrB,SAAS,GAAG,KAAK;MACjB,OAAOQ,MAAM,CAACQ,YAAY,CAAC,CAAC,CAAC;MAC7BT,MAAM,CAAC,IAAIe,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACrD,CAAC;IAEDV,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACb,MAAM,CAAC;EACnC,CAAC,CAAC;EAEF,OAAOV,WAAW;AACpB,CAAC;AAED,MAAMwB,kBAAkB,GAAGA,CAAC;EAAEC,QAAQ;EAAEvB,MAAM;EAAEC,SAAS,GAAG,CAAC,QAAQ;AAAE,CAAC,KAAK;EAAAuB,EAAA;EAC3E,MAAM,CAAClB,MAAM,EAAEmB,SAAS,CAAC,GAAGnC,QAAQ,CAACM,kBAAkB,CAAC;EACxD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAACM,kBAAkB,CAAC;EAC5D,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMwC,oBAAoB,GAAGvC,WAAW,CAAC,YAAY;IACnD,IAAI;MACF,IAAI,CAACS,MAAM,EAAE;QACX,MAAM,IAAImB,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMY,cAAc,GAAG,MAAMhC,iBAAiB,CAACC,MAAM,EAAEC,SAAS,CAAC;MACjEwB,SAAS,CAACM,cAAc,CAAC;MACzBJ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZC,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEI,GAAG,CAAC;MACjDH,QAAQ,CAACG,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAAChC,MAAM,EAAEC,SAAS,CAAC,CAAC;EAEvBZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACqC,OAAO,IAAI,CAACE,KAAK,EAAE;MACtBE,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEJ,OAAO,EAAEE,KAAK,CAAC,CAAC;EAE1C,MAAMM,YAAY,GAAG;IACnB5B,MAAM;IACNoB,OAAO;IACPE,KAAK;IACLO,MAAM,EAAEL;EACV,CAAC;EAED,oBACEpC,OAAA,CAACC,iBAAiB,CAACyC,QAAQ;IAACC,KAAK,EAAEH,YAAa;IAAAX,QAAA,EAC7CA;EAAQ;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEjC,CAAC;AAACjB,EAAA,CAtCIF,kBAAkB;AAAAoB,EAAA,GAAlBpB,kBAAkB;AAwCxB,OAAO,MAAMqB,aAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAMC,OAAO,GAAGzD,UAAU,CAACO,iBAAiB,CAAC;EAC7C,IAAI,CAACkD,OAAO,EAAE;IACZ,MAAM,IAAI1B,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EACA,OAAO0B,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARaD,aAAa;AAS1B,OAAO,MAAMG,cAAc,GAAGA,CAACC,gBAAgB,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAAA,IAAAC,GAAA,GAAAC,YAAA;EAChE,MAAMC,uBAAuB,GAAIC,KAAK,IAAK;IAAAH,GAAA;IACzC,MAAM;MAAE3C,MAAM;MAAEoB,OAAO;MAAEE;IAAM,CAAC,GAAGe,aAAa,CAAC,CAAC;IAElD,MAAMU,gBAAgB,GAAGL,OAAO,CAACK,gBAAgB,KAAK,mBAAM3D,OAAA;MAAA6B,QAAA,EAAK;IAAsB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,CAAC;IAC9F,MAAMa,cAAc,GAAGN,OAAO,CAACM,cAAc,KAAK,CAAC;MAAE1B;IAAM,CAAC,kBAC1DlC,OAAA;MAAA6B,QAAA,GAAK,6BAA2B,EAACK,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,OAAO;IAAA;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACtD,CAAC;IAEF,IAAIb,KAAK,EAAE;MACT,oBAAOlC,OAAA,CAAC4D,cAAc;QAAC1B,KAAK,EAAEA;MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC;IAEA,IAAI,CAACf,OAAO,IAAI,CAACpB,MAAM,EAAE;MACvB,oBAAOZ,OAAA,CAAC2D,gBAAgB;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7B;IAEA,oBAAO/C,OAAA,CAACqD,gBAAgB;MAAA,GAAKK,KAAK;MAAE9C,MAAM,EAAEA,MAAO;MAACkD,MAAM,EAAE9B;IAAQ;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzE,CAAC;EAACQ,GAAA,CAjBIE,uBAAuB;IAAA,QACQR,aAAa;EAAA;EAkBlDQ,uBAAuB,CAACM,WAAW,GAAI,kBAAiBV,gBAAgB,CAACU,WAAW,IAAIV,gBAAgB,CAACW,IAAK,GAAE;EAEhH,OAAOP,uBAAuB;AAChC,CAAC;;AAED;AACA,MAAMQ,yBAAyB,GAAGA,CAAC;EAAEpC;AAAS,CAAC,KAAK;EAClD,MAAMvB,MAAM,GAAGR,YAAY,CAAC,CAAC;EAE7B,oBACEE,OAAA,CAAC4B,kBAAkB;IAACtB,MAAM,EAAEA,MAAO;IAACC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAE;IAAAsB,QAAA,EACjEA;EAAQ;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEzB,CAAC;AAACmB,GAAA,GARID,yBAAyB;AAU/B,eAAeA,yBAAyB;AAAC,IAAAjB,EAAA,EAAAkB,GAAA;AAAAC,YAAA,CAAAnB,EAAA;AAAAmB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}