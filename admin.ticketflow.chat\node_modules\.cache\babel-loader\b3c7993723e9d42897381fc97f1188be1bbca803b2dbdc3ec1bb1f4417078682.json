{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\report-products\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Card, Col, Row, Space, Typography, Table, Tag, Button, DatePicker, Spin } from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport { clearCompare, fetchReportProduct, fetchReportProductChart, ReportProductCompare } from '../../redux/slices/report/products';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { t } from 'i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nimport shopService from '../../services/shop';\nimport { DebounceSelect } from 'components/search';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst ReportProducts = () => {\n  _s();\n  var _productList$data, _productList$data2, _productList$data3;\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const [shopId, setShopId] = useState();\n  const [perPageM, setPerPageM] = useState(10);\n  const {\n    date_from,\n    date_to,\n    by_time,\n    chart,\n    handleChart,\n    handleDateRange\n  } = useContext(ReportContext);\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const {\n    loading,\n    chartData: reportData,\n    productList\n  } = useSelector(state => state.productReport, shallowEqual);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const {\n    defaultCurrency\n  } = useSelector(state => state.currency, shallowEqual);\n  const expandedRowRender = row => {\n    const columns = [{\n      title: t('extras.name'),\n      dataIndex: 'Extras name',\n      render: (_, data) => {\n        var _data$extras;\n        return (_data$extras = data.extras) === null || _data$extras === void 0 ? void 0 : _data$extras.map(extra => {\n          var _extra$group$translat;\n          return (_extra$group$translat = extra.group.translation) === null || _extra$group$translat === void 0 ? void 0 : _extra$group$translat.title;\n        }).join(',');\n      },\n      key: 'Extras name'\n    }, {\n      title: t('item.sold'),\n      dataIndex: 'order_quantity',\n      key: 'order_quantity'\n    }, {\n      title: t('net.sales'),\n      dataIndex: 'upgradeNum',\n      render: (_, data) => numberToPrice(data.price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position),\n      key: 'upgradeNum'\n    }, {\n      title: t('orders'),\n      dataIndex: 'count',\n      key: 'count'\n    }, {\n      title: t('stock'),\n      dataIndex: 'quantity',\n      key: 'quantity'\n    }];\n    return /*#__PURE__*/_jsxDEV(Table, {\n      scroll: {\n        x: true\n      },\n      columns: columns,\n      dataSource: row.stocks,\n      pagination: false,\n      showHeader: false,\n      size: \"small\",\n      rowKey: stock => stock.id || stock.uuid || `stock-${stock.quantity}-${Math.random()}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  };\n  const [columns, setColumns] = useState([{\n    title: t('product.title'),\n    dataIndex: 'translation_title',\n    key: 'translation_title',\n    render: (_, data) => {\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `/report/products?product_id=${data.id}`,\n        children: data === null || data === void 0 ? void 0 : data.translation.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this);\n    },\n    is_show: true,\n    sorter: (a, b) => {\n      var _a$translation, _b$translation;\n      return a === null || a === void 0 ? void 0 : (_a$translation = a.translation) === null || _a$translation === void 0 ? void 0 : _a$translation.title.localeCompare(b === null || b === void 0 ? void 0 : (_b$translation = b.translation) === null || _b$translation === void 0 ? void 0 : _b$translation.title);\n    }\n  }, {\n    title: t('bar.code'),\n    dataIndex: 'bar_code',\n    key: 'bar_code',\n    is_show: true,\n    render: (_, data) => {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (data === null || data === void 0 ? void 0 : data.bar_code) || '-'\n      }, void 0, false);\n    }\n  }, {\n    title: t('item.sold'),\n    dataIndex: 'quantity',\n    key: 'quantity',\n    sorter: (a, b) => a.quantity - b.quantity,\n    is_show: true,\n    render: (_, data) => {\n      var _data$stocks;\n      const itemSold = data === null || data === void 0 ? void 0 : (_data$stocks = data.stocks) === null || _data$stocks === void 0 ? void 0 : _data$stocks.map(item => item.order_quantity).reduce((a, b) => a + b, 0);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: itemSold\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: t('net.sales'),\n    dataIndex: 'price',\n    key: 'price',\n    is_show: true,\n    render: price => numberToPrice(price, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position),\n    sorter: (a, b) => a.price - b.price\n  }, {\n    title: t('orders'),\n    key: 'count',\n    dataIndex: 'count',\n    is_show: true,\n    sorter: (a, b) => a.count - b.count\n  }, {\n    title: t('category'),\n    key: 'category',\n    dataIndex: 'category',\n    is_show: true,\n    render: (_, row) => {\n      var _row$category, _row$category2, _row$category2$transl;\n      return /*#__PURE__*/_jsxDEV(Link, {\n        to: `/report/products?category_id=${(_row$category = row.category) === null || _row$category === void 0 ? void 0 : _row$category.id}`,\n        children: row === null || row === void 0 ? void 0 : (_row$category2 = row.category) === null || _row$category2 === void 0 ? void 0 : (_row$category2$transl = _row$category2.translation) === null || _row$category2$transl === void 0 ? void 0 : _row$category2$transl.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: t('status'),\n    key: 'active',\n    dataIndex: 'active',\n    render: (_, data) => {\n      const status = Boolean(data === null || data === void 0 ? void 0 : data.active);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: status ? 'green' : 'red',\n        children: status ? t('active') : t('inactive')\n      }, data.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this);\n    },\n    is_show: true\n  }]);\n  const chart_type = useMemo(() => [{\n    label: 'item.sold',\n    value: 'quantity',\n    qty: 'quantity',\n    price: false\n  }, {\n    label: 'net.sales',\n    value: 'price',\n    qty: 'price',\n    price: true\n  }, {\n    label: t('orders'),\n    value: 'count',\n    qty: 'count',\n    price: false\n  }], []);\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (chart_type.find(item => item.value === chart)) {\n      dispatch(fetchReportProductChart(params));\n    }\n  };\n  const fetchShops = search => {\n    const params = {\n      perPage: 10,\n      search\n    };\n    return shopService.selectPaginate(params).then(res => res === null || res === void 0 ? void 0 : res.map(shop => {\n      var _shop$translation;\n      return {\n        label: shop === null || shop === void 0 ? void 0 : (_shop$translation = shop.translation) === null || _shop$translation === void 0 ? void 0 : _shop$translation.title,\n        value: shop === null || shop === void 0 ? void 0 : shop.id\n      };\n    })).catch(err => console.log('report product ERROR => ', err));\n  };\n  const fetchProduct = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (shopId) params.shop_id = shopId;\n    dispatch(fetchReportProduct(params));\n  };\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchProduct();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n  useDidUpdate(() => {\n    fetchProduct();\n  }, [date_to, search, category_id, product_id, by_time, date_from, shopId]);\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n  const onChangePagination = pagination => {\n    const {\n      pageSize: perPage,\n      current: page\n    } = pagination;\n    setPerPageM(perPage);\n    fetchProduct(page, perPage);\n  };\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      shop_id: shopId,\n      products: rowSelection !== null && rowSelection !== void 0 && rowSelection.selectedRowKeys[0] ? rowSelection === null || rowSelection === void 0 ? void 0 : rowSelection.selectedRowKeys : product_id ? [product_id] : undefined\n    }).then(res => {\n      const body = export_url + res.data.file_name;\n      window.location.href = body;\n    }).finally(() => setDownloading(false));\n  };\n  const onSelectChange = newSelectedRowKeys => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange\n  };\n  const Compare = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n      ids: selectedRowKeys,\n      shop_id: shopId\n    };\n    dispatch(ReportProductCompare(params));\n  };\n  const clear = () => {\n    dispatch(clearCompare());\n    setShopId(undefined);\n    setSelectedRowKeys([]);\n    fetchProduct();\n    fetchReport();\n    navigate(`/report/products`);\n  };\n  const onShopSelectClear = () => {\n    setShopId(undefined);\n    fetchReportProduct();\n    fetchReportProductChart({});\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    size: \"large\",\n    spinning: loading,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            ...configureRangePicker(),\n            defaultValue: [moment(date_from), moment(date_to)],\n            onChange: handleDateRange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      className: \"report-products\",\n      children: chart_type === null || chart_type === void 0 ? void 0 : chart_type.map(item => /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        onClick: () => handleChart(item.value),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: chart === item.value && 'active',\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-5\",\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                children: t(item.label)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 24,\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Title, {\n                level: 2,\n                children: !(item !== null && item !== void 0 && item.price) ? reportData[item.qty] : numberToPrice(reportData[item.qty], defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.symbol, defaultCurrency === null || defaultCurrency === void 0 ? void 0 : defaultCurrency.position)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)\n      }, item.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReportChart, {\n      reportData: reportData,\n      chart_data: \"quantities_sum\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        className: \"d-flex justify-content-between align-center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n          strong: true,\n          level: 3,\n          children: t('products')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          className: \"d-flex justify-content-between\",\n          children: [/*#__PURE__*/_jsxDEV(SearchInput, {\n            style: {\n              minWidth: '300px'\n            },\n            handleChange: e => setSearch(e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DebounceSelect, {\n            fetchOptions: fetchShops,\n            placeholder: t('select.shop'),\n            onSelect: value => setShopId(value.value),\n            onClear: () => onShopSelectClear()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: Boolean(selectedRowKeys.length) || !!category_id || !!product_id || !!shopId ? 'primary' : 'default',\n            danger: Boolean(selectedRowKeys.length) || !!category_id || !!product_id || !!shopId,\n            onClick: clear,\n            children: t('clear')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(CloudDownloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 21\n            }, this),\n            loading: downloading,\n            onClick: excelExport,\n            children: t('download')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FilterColumns, {\n            columns: columns,\n            setColumns: setColumns\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        expandable: {\n          expandedRowRender,\n          defaultExpandedRowKeys: ['0']\n        },\n        rowSelection: rowSelection,\n        columns: columns === null || columns === void 0 ? void 0 : columns.filter(item => item.is_show),\n        dataSource: (_productList$data = productList.data) === null || _productList$data === void 0 ? void 0 : _productList$data.data,\n        rowKey: row => row.id,\n        loading: loading,\n        pagination: {\n          pageSize: perPageM,\n          page: (productList === null || productList === void 0 ? void 0 : (_productList$data2 = productList.data) === null || _productList$data2 === void 0 ? void 0 : _productList$data2.meta.page) || 1,\n          total: productList === null || productList === void 0 ? void 0 : (_productList$data3 = productList.data) === null || _productList$data3 === void 0 ? void 0 : _productList$data3.meta.total,\n          defaultCurrent: 1\n        },\n        onChange: onChangePagination,\n        scroll: {\n          x: 1500\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportProducts, \"VNVH1wVd6fM8XKPdvvJegw2kAdY=\", false, function () {\n  return [useDispatch, useLocation, useNavigate, useSelector, useSelector, useSelector, useDidUpdate, useDidUpdate];\n});\n_c = ReportProducts;\nexport default ReportProducts;\nvar _c;\n$RefreshReg$(_c, \"ReportProducts\");", "map": {"version": 3, "names": ["Card", "Col", "Row", "Space", "Typography", "Table", "Tag", "<PERSON><PERSON>", "DatePicker", "Spin", "React", "useContext", "useEffect", "useState", "SearchInput", "CloudDownloadOutlined", "ReportService", "disable<PERSON><PERSON><PERSON><PERSON>", "shallowEqual", "useDispatch", "useSelector", "ReportChart", "moment", "ReportContext", "FilterColumns", "export_url", "configureRangePicker", "clearCompare", "fetchReportProduct", "fetchReportProductChart", "ReportProductCompare", "useDidUpdate", "Link", "useLocation", "useNavigate", "QueryString", "t", "numberToPrice", "useMemo", "shopService", "DebounceSelect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "Title", "RangePicker", "ReportProducts", "_s", "_productList$data", "_productList$data2", "_productList$data3", "dispatch", "location", "navigate", "category_id", "parse", "search", "product_id", "shopId", "setShopId", "perPageM", "setPerPageM", "date_from", "date_to", "by_time", "chart", "handleChart", "handleDateRange", "activeMenu", "state", "menu", "loading", "chartData", "reportData", "productList", "productReport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "downloading", "setDownloading", "setSearch", "defaultCurrency", "currency", "expandedRowRender", "row", "columns", "title", "dataIndex", "render", "_", "data", "_data$extras", "extras", "map", "extra", "_extra$group$translat", "group", "translation", "join", "key", "price", "symbol", "position", "scroll", "x", "dataSource", "stocks", "pagination", "showHeader", "size", "<PERSON><PERSON><PERSON>", "stock", "id", "uuid", "quantity", "Math", "random", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "setColumns", "to", "children", "is_show", "sorter", "a", "b", "_a$translation", "_b$translation", "localeCompare", "bar_code", "_data$stocks", "itemSold", "item", "order_quantity", "reduce", "count", "_row$category", "_row$category2", "_row$category2$transl", "category", "status", "Boolean", "active", "color", "chart_type", "label", "value", "qty", "fetchReport", "params", "type", "categories", "products", "find", "fetchShops", "perPage", "selectPaginate", "then", "res", "shop", "_shop$translation", "catch", "err", "console", "log", "fetchProduct", "page", "shop_id", "refetch", "onChangePagination", "pageSize", "current", "excelExport", "getReportProductList", "export", "rowSelection", "undefined", "body", "file_name", "window", "href", "finally", "onSelectChange", "newSelectedRowKeys", "onChange", "Compare", "ids", "clear", "onShopSelectClear", "spinning", "gutter", "className", "span", "defaultValue", "onClick", "level", "chart_data", "strong", "style", "min<PERSON><PERSON><PERSON>", "handleChange", "e", "fetchOptions", "placeholder", "onSelect", "onClear", "length", "danger", "icon", "expandable", "defaultExpandedRowKeys", "filter", "meta", "total", "defaultCurrent", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/report-products/index.js"], "sourcesContent": ["import {\n  <PERSON>,\n  Col,\n  Row,\n  Space,\n  Typography,\n  Table,\n  Tag,\n  <PERSON><PERSON>,\n  DatePicker,\n  Spin,\n} from 'antd';\nimport React, { useContext, useEffect, useState } from 'react';\nimport SearchInput from '../../components/search-input';\nimport { CloudDownloadOutlined } from '@ant-design/icons';\nimport ReportService from '../../services/reports';\nimport { disableRefetch } from '../../redux/slices/menu';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport ReportChart from '../../components/report/chart';\nimport moment from 'moment';\nimport { ReportContext } from '../../context/report';\nimport FilterColumns from '../../components/filter-column';\nimport { export_url } from '../../configs/app-global';\nimport { configureRangePicker } from '../../configs/datepicker-config';\nimport {\n  clearCompare,\n  fetchReportProduct,\n  fetchReportProduct<PERSON>hart,\n  ReportProductCompare,\n} from '../../redux/slices/report/products';\nimport useDidUpdate from '../../helpers/useDidUpdate';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport QueryString from 'qs';\nimport { t } from 'i18next';\nimport numberToPrice from '../../helpers/numberToPrice';\nimport { useMemo } from 'react';\nimport shopService from '../../services/shop';\nimport { DebounceSelect } from 'components/search';\nconst { Text, Title } = Typography;\nconst { RangePicker } = DatePicker;\n\nconst ReportProducts = () => {\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const category_id = QueryString.parse(location.search, [])['?category_id'];\n  const product_id = QueryString.parse(location.search, [])['?product_id'];\n  const [shopId, setShopId] = useState();\n  const [perPageM, setPerPageM] = useState(10);\n  const { date_from, date_to, by_time, chart, handleChart, handleDateRange } =\n    useContext(ReportContext);\n\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  const {\n    loading,\n    chartData: reportData,\n    productList,\n  } = useSelector((state) => state.productReport, shallowEqual);\n\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [downloading, setDownloading] = useState(false);\n  const [search, setSearch] = useState('');\n  const { defaultCurrency } = useSelector(\n    (state) => state.currency,\n    shallowEqual,\n  );\n\n  const expandedRowRender = (row) => {\n    const columns = [\n      {\n        title: t('extras.name'),\n        dataIndex: 'Extras name',\n        render: (_, data) =>\n          data.extras?.map((extra) => extra.group.translation?.title).join(','),\n        key: 'Extras name',\n      },\n      {\n        title: t('item.sold'),\n        dataIndex: 'order_quantity',\n        key: 'order_quantity',\n      },\n      {\n        title: t('net.sales'),\n        dataIndex: 'upgradeNum',\n        render: (_, data) =>\n          numberToPrice(\n            data.price,\n            defaultCurrency?.symbol,\n            defaultCurrency?.position,\n          ),\n        key: 'upgradeNum',\n      },\n      {\n        title: t('orders'),\n        dataIndex: 'count',\n        key: 'count',\n      },\n      {\n        title: t('stock'),\n        dataIndex: 'quantity',\n        key: 'quantity',\n      },\n    ];\n    return (\n      <Table\n        scroll={{ x: true }}\n        columns={columns}\n        dataSource={row.stocks}\n        pagination={false}\n        showHeader={false}\n        size=\"small\"\n        rowKey={(stock) => stock.id || stock.uuid || `stock-${stock.quantity}-${Math.random()}`}\n      />\n    );\n  };\n\n  const [columns, setColumns] = useState([\n    {\n      title: t('product.title'),\n      dataIndex: 'translation_title',\n      key: 'translation_title',\n      render: (_, data) => {\n        return (\n          <Link to={`/report/products?product_id=${data.id}`}>\n            {data?.translation.title}\n          </Link>\n        );\n      },\n      is_show: true,\n      sorter: (a, b) =>\n        a?.translation?.title.localeCompare(b?.translation?.title),\n    },\n    {\n      title: t('bar.code'),\n      dataIndex: 'bar_code',\n      key: 'bar_code',\n      is_show: true,\n      render: (_, data) => {\n        return <>{data?.bar_code || '-'}</>;\n      },\n    },\n    {\n      title: t('item.sold'),\n      dataIndex: 'quantity',\n      key: 'quantity',\n      sorter: (a, b) => a.quantity - b.quantity,\n      is_show: true,\n      render: (_, data) => {\n        const itemSold = data?.stocks\n          ?.map((item) => item.order_quantity)\n          .reduce((a, b) => a + b, 0);\n        return <div>{itemSold}</div>;\n      },\n    },\n    {\n      title: t('net.sales'),\n      dataIndex: 'price',\n      key: 'price',\n      is_show: true,\n      render: (price) =>\n        numberToPrice(\n          price,\n          defaultCurrency?.symbol,\n          defaultCurrency?.position,\n        ),\n      sorter: (a, b) => a.price - b.price,\n    },\n    {\n      title: t('orders'),\n      key: 'count',\n      dataIndex: 'count',\n      is_show: true,\n      sorter: (a, b) => a.count - b.count,\n    },\n    {\n      title: t('category'),\n      key: 'category',\n      dataIndex: 'category',\n      is_show: true,\n      render: (_, row) => {\n        return (\n          <Link to={`/report/products?category_id=${row.category?.id}`}>\n            {row?.category?.translation?.title}\n          </Link>\n        );\n      },\n    },\n    {\n      title: t('status'),\n      key: 'active',\n      dataIndex: 'active',\n      render: (_, data) => {\n        const status = Boolean(data?.active);\n        return (\n          <Tag color={status ? 'green' : 'red'} key={data.id}>\n            {status ? t('active') : t('inactive')}\n          </Tag>\n        );\n      },\n      is_show: true,\n    },\n  ]);\n\n  const chart_type = useMemo(\n    () => [\n      {\n        label: 'item.sold',\n        value: 'quantity',\n        qty: 'quantity',\n        price: false,\n      },\n      { label: 'net.sales', value: 'price', qty: 'price', price: true },\n      { label: t('orders'), value: 'count', qty: 'count', price: false },\n    ],\n    [],\n  );\n\n  const fetchReport = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (chart_type.find((item) => item.value === chart)) {\n      dispatch(fetchReportProductChart(params));\n    }\n  };\n\n  const fetchShops = (search) => {\n    const params = {\n      perPage: 10,\n      search,\n    };\n    return shopService\n      .selectPaginate(params)\n      .then((res) =>\n        res?.map((shop) => ({\n          label: shop?.translation?.title,\n          value: shop?.id,\n        })),\n      )\n      .catch((err) => console.log('report product ERROR => ', err));\n  };\n\n  const fetchProduct = (page, perPage) => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      page,\n      perPage,\n      search: search || null,\n    };\n    if (category_id) params.categories = [category_id];\n    if (product_id) params.products = [product_id];\n    if (shopId) params.shop_id = shopId;\n    dispatch(fetchReportProduct(params));\n  };\n\n  useEffect(() => {\n    handleChart(chart_type[0].value);\n  }, []);\n\n  useEffect(() => {\n    if (activeMenu.refetch) {\n      fetchProduct();\n      fetchReport();\n      dispatch(disableRefetch(activeMenu));\n    }\n  }, [activeMenu.refetch]);\n\n  useDidUpdate(() => {\n    fetchProduct();\n  }, [date_to, search, category_id, product_id, by_time, date_from, shopId]);\n\n  useDidUpdate(() => {\n    fetchReport();\n  }, [date_to, by_time, chart, category_id, product_id, date_from]);\n\n  const onChangePagination = (pagination) => {\n    const { pageSize: perPage, current: page } = pagination;\n    setPerPageM(perPage);\n    fetchProduct(page, perPage);\n  };\n\n  const excelExport = () => {\n    setDownloading(true);\n    ReportService.getReportProductList({\n      date_from,\n      date_to,\n      type: by_time,\n      export: 'excel',\n      shop_id: shopId,\n      products: rowSelection?.selectedRowKeys[0]\n        ? rowSelection?.selectedRowKeys\n        : product_id\n        ? [product_id]\n        : undefined,\n    })\n      .then((res) => {\n        const body = export_url + res.data.file_name;\n        window.location.href = body;\n      })\n      .finally(() => setDownloading(false));\n  };\n\n  const onSelectChange = (newSelectedRowKeys) => {\n    setSelectedRowKeys(newSelectedRowKeys);\n  };\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: onSelectChange,\n  };\n\n  const Compare = () => {\n    const params = {\n      date_from,\n      date_to,\n      type: by_time,\n      chart,\n      ids: selectedRowKeys,\n      shop_id: shopId,\n    };\n\n    dispatch(ReportProductCompare(params));\n  };\n\n  const clear = () => {\n    dispatch(clearCompare());\n    setShopId(undefined);\n    setSelectedRowKeys([]);\n    fetchProduct();\n    fetchReport();\n    navigate(`/report/products`);\n  };\n\n  const onShopSelectClear = () => {\n    setShopId(undefined);\n    fetchReportProduct();\n    fetchReportProductChart({});\n  };\n\n  return (\n    <Spin size='large' spinning={loading}>\n      <Row gutter={24} className='mb-3'>\n        <Col span={12}>\n          <Space>\n            <RangePicker\n              {...configureRangePicker()}\n              defaultValue={[moment(date_from), moment(date_to)]}\n              onChange={handleDateRange}\n            />\n          </Space>\n        </Col>\n      </Row>\n      <Row gutter={24} className='report-products'>\n        {chart_type?.map((item) => (\n          <Col\n            span={8}\n            key={item.label}\n            onClick={() => handleChart(item.value)}\n          >\n            <Card className={chart === item.value && 'active'}>\n              <Row className='mb-5'>\n                <Col>\n                  <Text>{t(item.label)}</Text>\n                </Col>\n              </Row>\n              <Row gutter={24}>\n                <Col span={12}>\n                  <Title level={2}>\n                    {!item?.price\n                      ? reportData[item.qty]\n                      : numberToPrice(\n                          reportData[item.qty],\n                          defaultCurrency?.symbol,\n                          defaultCurrency?.position,\n                        )}\n                  </Title>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n      <ReportChart reportData={reportData} chart_data='quantities_sum' />\n      <Card>\n        <Space className='d-flex justify-content-between align-center'>\n          <Typography.Text strong level={3}>\n            {t('products')}\n          </Typography.Text>\n          <Space className='d-flex justify-content-between'>\n            <SearchInput\n              style={{ minWidth: '300px' }}\n              handleChange={(e) => setSearch(e)}\n            />\n            <DebounceSelect\n              fetchOptions={fetchShops}\n              placeholder={t('select.shop')}\n              onSelect={(value) => setShopId(value.value)}\n              onClear={() => onShopSelectClear()}\n            />\n            <Button\n              type={\n                Boolean(selectedRowKeys.length) ||\n                !!category_id ||\n                !!product_id ||\n                !!shopId\n                  ? 'primary'\n                  : 'default'\n              }\n              danger={\n                Boolean(selectedRowKeys.length) ||\n                !!category_id ||\n                !!product_id ||\n                !!shopId\n              }\n              onClick={clear}\n            >\n              {t('clear')}\n            </Button>\n            <Button\n              icon={<CloudDownloadOutlined />}\n              loading={downloading}\n              onClick={excelExport}\n            >\n              {t('download')}\n            </Button>\n            <FilterColumns columns={columns} setColumns={setColumns} />\n          </Space>\n        </Space>\n\n        <Table\n          expandable={{\n            expandedRowRender,\n            defaultExpandedRowKeys: ['0'],\n          }}\n          rowSelection={rowSelection}\n          columns={columns?.filter((item) => item.is_show)}\n          dataSource={productList.data?.data}\n          rowKey={(row) => row.id}\n          loading={loading}\n          pagination={{\n            pageSize: perPageM,\n            page: productList?.data?.meta.page || 1,\n            total: productList?.data?.meta.total,\n            defaultCurrent: 1,\n          }}\n          onChange={onChangePagination}\n          scroll={{\n            x: 1500,\n          }}\n        />\n      </Card>\n    </Spin>\n  );\n};\n\nexport default ReportProducts;\n"], "mappings": ";;AAAA,SACEA,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,IAAI,QACC,MAAM;AACb,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,OAAOC,WAAW,MAAM,+BAA+B;AACvD,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SACEC,YAAY,EACZC,kBAAkB,EAClBC,uBAAuB,EACvBC,oBAAoB,QACf,oCAAoC;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,WAAW,MAAM,IAAI;AAC5B,SAASC,CAAC,QAAQ,SAAS;AAC3B,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACnD,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAG1C,UAAU;AAClC,MAAM;EAAE2C;AAAY,CAAC,GAAGvC,UAAU;AAElC,MAAMwC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;EAC3B,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,WAAW,GAAGrB,WAAW,CAACsB,KAAK,CAACH,QAAQ,CAACI,MAAM,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC;EAC1E,MAAMC,UAAU,GAAGxB,WAAW,CAACsB,KAAK,CAACH,QAAQ,CAACI,MAAM,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC;EACxE,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,CAAC;EACtC,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEmD,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GACxE1D,UAAU,CAACY,aAAa,CAAC;EAE3B,MAAM;IAAE+C;EAAW,CAAC,GAAGlD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEtD,YAAY,CAAC;EAEvE,MAAM;IACJuD,OAAO;IACPC,SAAS,EAAEC,UAAU;IACrBC;EACF,CAAC,GAAGxD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACM,aAAa,EAAE3D,YAAY,CAAC;EAE7D,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,MAAM,EAAEwB,SAAS,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM;IAAEsE;EAAgB,CAAC,GAAG/D,WAAW,CACpCmD,KAAK,IAAKA,KAAK,CAACa,QAAQ,EACzBlE,YACF,CAAC;EAED,MAAMmE,iBAAiB,GAAIC,GAAG,IAAK;IACjC,MAAMC,OAAO,GAAG,CACd;MACEC,KAAK,EAAEpD,CAAC,CAAC,aAAa,CAAC;MACvBqD,SAAS,EAAE,aAAa;MACxBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI;QAAA,IAAAC,YAAA;QAAA,QAAAA,YAAA,GACdD,IAAI,CAACE,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,GAAG,CAAEC,KAAK;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAKD,KAAK,CAACE,KAAK,CAACC,WAAW,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBT,KAAK;QAAA,EAAC,CAACY,IAAI,CAAC,GAAG,CAAC;MAAA;MACvEC,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;MACrBqD,SAAS,EAAE,gBAAgB;MAC3BY,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;MACrBqD,SAAS,EAAE,YAAY;MACvBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KACdvD,aAAa,CACXuD,IAAI,CAACU,KAAK,EACVnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB,CAAC;MACHH,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;MAClBqD,SAAS,EAAE,OAAO;MAClBY,GAAG,EAAE;IACP,CAAC,EACD;MACEb,KAAK,EAAEpD,CAAC,CAAC,OAAO,CAAC;MACjBqD,SAAS,EAAE,UAAU;MACrBY,GAAG,EAAE;IACP,CAAC,CACF;IACD,oBACE3D,OAAA,CAACrC,KAAK;MACJoG,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK,CAAE;MACpBnB,OAAO,EAAEA,OAAQ;MACjBoB,UAAU,EAAErB,GAAG,CAACsB,MAAO;MACvBC,UAAU,EAAE,KAAM;MAClBC,UAAU,EAAE,KAAM;MAClBC,IAAI,EAAC,OAAO;MACZC,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACC,EAAE,IAAID,KAAK,CAACE,IAAI,IAAK,SAAQF,KAAK,CAACG,QAAS,IAAGC,IAAI,CAACC,MAAM,CAAC,CAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC;EAEN,CAAC;EAED,MAAM,CAACnC,OAAO,EAAEoC,UAAU,CAAC,GAAG9G,QAAQ,CAAC,CACrC;IACE2E,KAAK,EAAEpD,CAAC,CAAC,eAAe,CAAC;IACzBqD,SAAS,EAAE,mBAAmB;IAC9BY,GAAG,EAAE,mBAAmB;IACxBX,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,oBACElD,OAAA,CAACV,IAAI;QAAC4F,EAAE,EAAG,+BAA8BhC,IAAI,CAACsB,EAAG,EAAE;QAAAW,QAAA,EAChDjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,WAAW,CAACX;MAAK;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEX,CAAC;IACDI,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC;MAAA,IAAAC,cAAA,EAAAC,cAAA;MAAA,OACXH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAE7B,WAAW,cAAA+B,cAAA,uBAAdA,cAAA,CAAgB1C,KAAK,CAAC4C,aAAa,CAACH,CAAC,aAADA,CAAC,wBAAAE,cAAA,GAADF,CAAC,CAAE9B,WAAW,cAAAgC,cAAA,uBAAdA,cAAA,CAAgB3C,KAAK,CAAC;IAAA;EAC9D,CAAC,EACD;IACEA,KAAK,EAAEpD,CAAC,CAAC,UAAU,CAAC;IACpBqD,SAAS,EAAE,UAAU;IACrBY,GAAG,EAAE,UAAU;IACfyB,OAAO,EAAE,IAAI;IACbpC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,oBAAOlD,OAAA,CAAAE,SAAA;QAAAiF,QAAA,EAAG,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,QAAQ,KAAI;MAAG,gBAAG,CAAC;IACrC;EACF,CAAC,EACD;IACE7C,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;IACrBqD,SAAS,EAAE,UAAU;IACrBY,GAAG,EAAE,UAAU;IACf0B,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACZ,QAAQ,GAAGa,CAAC,CAACb,QAAQ;IACzCU,OAAO,EAAE,IAAI;IACbpC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MAAA,IAAA0C,YAAA;MACnB,MAAMC,QAAQ,GAAG3C,IAAI,aAAJA,IAAI,wBAAA0C,YAAA,GAAJ1C,IAAI,CAAEgB,MAAM,cAAA0B,YAAA,uBAAZA,YAAA,CACbvC,GAAG,CAAEyC,IAAI,IAAKA,IAAI,CAACC,cAAc,CAAC,CACnCC,MAAM,CAAC,CAACV,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;MAC7B,oBAAOvF,OAAA;QAAAmF,QAAA,EAAMU;MAAQ;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9B;EACF,CAAC,EACD;IACElC,KAAK,EAAEpD,CAAC,CAAC,WAAW,CAAC;IACrBqD,SAAS,EAAE,OAAO;IAClBY,GAAG,EAAE,OAAO;IACZyB,OAAO,EAAE,IAAI;IACbpC,MAAM,EAAGY,KAAK,IACZjE,aAAa,CACXiE,KAAK,EACLnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB,CAAC;IACHuB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC1B,KAAK,GAAG2B,CAAC,CAAC3B;EAChC,CAAC,EACD;IACEd,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;IAClBiE,GAAG,EAAE,OAAO;IACZZ,SAAS,EAAE,OAAO;IAClBqC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACW,KAAK,GAAGV,CAAC,CAACU;EAChC,CAAC,EACD;IACEnD,KAAK,EAAEpD,CAAC,CAAC,UAAU,CAAC;IACpBiE,GAAG,EAAE,UAAU;IACfZ,SAAS,EAAE,UAAU;IACrBqC,OAAO,EAAE,IAAI;IACbpC,MAAM,EAAEA,CAACC,CAAC,EAAEL,GAAG,KAAK;MAAA,IAAAsD,aAAA,EAAAC,cAAA,EAAAC,qBAAA;MAClB,oBACEpG,OAAA,CAACV,IAAI;QAAC4F,EAAE,EAAG,gCAA6B,CAAAgB,aAAA,GAAEtD,GAAG,CAACyD,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAc1B,EAAG,EAAE;QAAAW,QAAA,EAC1DvC,GAAG,aAAHA,GAAG,wBAAAuD,cAAA,GAAHvD,GAAG,CAAEyD,QAAQ,cAAAF,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe1C,WAAW,cAAA2C,qBAAA,uBAA1BA,qBAAA,CAA4BtD;MAAK;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAEX;EACF,CAAC,EACD;IACElC,KAAK,EAAEpD,CAAC,CAAC,QAAQ,CAAC;IAClBiE,GAAG,EAAE,QAAQ;IACbZ,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;MACnB,MAAMoD,MAAM,GAAGC,OAAO,CAACrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,MAAM,CAAC;MACpC,oBACExG,OAAA,CAACpC,GAAG;QAAC6I,KAAK,EAAEH,MAAM,GAAG,OAAO,GAAG,KAAM;QAAAnB,QAAA,EAClCmB,MAAM,GAAG5G,CAAC,CAAC,QAAQ,CAAC,GAAGA,CAAC,CAAC,UAAU;MAAC,GADIwD,IAAI,CAACsB,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7C,CAAC;IAEV,CAAC;IACDI,OAAO,EAAE;EACX,CAAC,CACF,CAAC;EAEF,MAAMsB,UAAU,GAAG9G,OAAO,CACxB,MAAM,CACJ;IACE+G,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,UAAU;IACfjD,KAAK,EAAE;EACT,CAAC,EACD;IAAE+C,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEjD,KAAK,EAAE;EAAK,CAAC,EACjE;IAAE+C,KAAK,EAAEjH,CAAC,CAAC,QAAQ,CAAC;IAAEkH,KAAK,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEjD,KAAK,EAAE;EAAM,CAAC,CACnE,EACD,EACF,CAAC;EAED,MAAMkD,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,MAAM,GAAG;MACbzF,SAAS;MACTC,OAAO;MACPyF,IAAI,EAAExF,OAAO;MACbC;IACF,CAAC;IACD,IAAIX,WAAW,EAAEiG,MAAM,CAACE,UAAU,GAAG,CAACnG,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAE8F,MAAM,CAACG,QAAQ,GAAG,CAACjG,UAAU,CAAC;IAC9C,IAAIyF,UAAU,CAACS,IAAI,CAAErB,IAAI,IAAKA,IAAI,CAACc,KAAK,KAAKnF,KAAK,CAAC,EAAE;MACnDd,QAAQ,CAACxB,uBAAuB,CAAC4H,MAAM,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMK,UAAU,GAAIpG,MAAM,IAAK;IAC7B,MAAM+F,MAAM,GAAG;MACbM,OAAO,EAAE,EAAE;MACXrG;IACF,CAAC;IACD,OAAOnB,WAAW,CACfyH,cAAc,CAACP,MAAM,CAAC,CACtBQ,IAAI,CAAEC,GAAG,IACRA,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEnE,GAAG,CAAEoE,IAAI;MAAA,IAAAC,iBAAA;MAAA,OAAM;QAClBf,KAAK,EAAEc,IAAI,aAAJA,IAAI,wBAAAC,iBAAA,GAAJD,IAAI,CAAEhE,WAAW,cAAAiE,iBAAA,uBAAjBA,iBAAA,CAAmB5E,KAAK;QAC/B8D,KAAK,EAAEa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjD;MACf,CAAC;IAAA,CAAC,CACJ,CAAC,CACAmD,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,GAAG,CAAC,CAAC;EACjE,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACC,IAAI,EAAEX,OAAO,KAAK;IACtC,MAAMN,MAAM,GAAG;MACbzF,SAAS;MACTC,OAAO;MACPyF,IAAI,EAAExF,OAAO;MACbwG,IAAI;MACJX,OAAO;MACPrG,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC;IACD,IAAIF,WAAW,EAAEiG,MAAM,CAACE,UAAU,GAAG,CAACnG,WAAW,CAAC;IAClD,IAAIG,UAAU,EAAE8F,MAAM,CAACG,QAAQ,GAAG,CAACjG,UAAU,CAAC;IAC9C,IAAIC,MAAM,EAAE6F,MAAM,CAACkB,OAAO,GAAG/G,MAAM;IACnCP,QAAQ,CAACzB,kBAAkB,CAAC6H,MAAM,CAAC,CAAC;EACtC,CAAC;EAED7I,SAAS,CAAC,MAAM;IACdwD,WAAW,CAACgF,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN1I,SAAS,CAAC,MAAM;IACd,IAAI0D,UAAU,CAACsG,OAAO,EAAE;MACtBH,YAAY,CAAC,CAAC;MACdjB,WAAW,CAAC,CAAC;MACbnG,QAAQ,CAACpC,cAAc,CAACqD,UAAU,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,UAAU,CAACsG,OAAO,CAAC,CAAC;EAExB7I,YAAY,CAAC,MAAM;IACjB0I,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACxG,OAAO,EAAEP,MAAM,EAAEF,WAAW,EAAEG,UAAU,EAAEO,OAAO,EAAEF,SAAS,EAAEJ,MAAM,CAAC,CAAC;EAE1E7B,YAAY,CAAC,MAAM;IACjByH,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACvF,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEX,WAAW,EAAEG,UAAU,EAAEK,SAAS,CAAC,CAAC;EAEjE,MAAM6G,kBAAkB,GAAIhE,UAAU,IAAK;IACzC,MAAM;MAAEiE,QAAQ,EAAEf,OAAO;MAAEgB,OAAO,EAAEL;IAAK,CAAC,GAAG7D,UAAU;IACvD9C,WAAW,CAACgG,OAAO,CAAC;IACpBU,YAAY,CAACC,IAAI,EAAEX,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB/F,cAAc,CAAC,IAAI,CAAC;IACpBjE,aAAa,CAACiK,oBAAoB,CAAC;MACjCjH,SAAS;MACTC,OAAO;MACPyF,IAAI,EAAExF,OAAO;MACbgH,MAAM,EAAE,OAAO;MACfP,OAAO,EAAE/G,MAAM;MACfgG,QAAQ,EAAEuB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAErG,eAAe,CAAC,CAAC,CAAC,GACtCqG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAErG,eAAe,GAC7BnB,UAAU,GACV,CAACA,UAAU,CAAC,GACZyH;IACN,CAAC,CAAC,CACCnB,IAAI,CAAEC,GAAG,IAAK;MACb,MAAMmB,IAAI,GAAG5J,UAAU,GAAGyI,GAAG,CAACtE,IAAI,CAAC0F,SAAS;MAC5CC,MAAM,CAACjI,QAAQ,CAACkI,IAAI,GAAGH,IAAI;IAC7B,CAAC,CAAC,CACDI,OAAO,CAAC,MAAMxG,cAAc,CAAC,KAAK,CAAC,CAAC;EACzC,CAAC;EAED,MAAMyG,cAAc,GAAIC,kBAAkB,IAAK;IAC7C5G,kBAAkB,CAAC4G,kBAAkB,CAAC;EACxC,CAAC;EAED,MAAMR,YAAY,GAAG;IACnBrG,eAAe;IACf8G,QAAQ,EAAEF;EACZ,CAAC;EAED,MAAMG,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMpC,MAAM,GAAG;MACbzF,SAAS;MACTC,OAAO;MACPyF,IAAI,EAAExF,OAAO;MACbC,KAAK;MACL2H,GAAG,EAAEhH,eAAe;MACpB6F,OAAO,EAAE/G;IACX,CAAC;IAEDP,QAAQ,CAACvB,oBAAoB,CAAC2H,MAAM,CAAC,CAAC;EACxC,CAAC;EAED,MAAMsC,KAAK,GAAGA,CAAA,KAAM;IAClB1I,QAAQ,CAAC1B,YAAY,CAAC,CAAC,CAAC;IACxBkC,SAAS,CAACuH,SAAS,CAAC;IACpBrG,kBAAkB,CAAC,EAAE,CAAC;IACtB0F,YAAY,CAAC,CAAC;IACdjB,WAAW,CAAC,CAAC;IACbjG,QAAQ,CAAE,kBAAiB,CAAC;EAC9B,CAAC;EAED,MAAMyI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnI,SAAS,CAACuH,SAAS,CAAC;IACpBxJ,kBAAkB,CAAC,CAAC;IACpBC,uBAAuB,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC;EAED,oBACEa,OAAA,CAACjC,IAAI;IAACsG,IAAI,EAAC,OAAO;IAACkF,QAAQ,EAAExH,OAAQ;IAAAoD,QAAA,gBACnCnF,OAAA,CAACxC,GAAG;MAACgM,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,MAAM;MAAAtE,QAAA,eAC/BnF,OAAA,CAACzC,GAAG;QAACmM,IAAI,EAAE,EAAG;QAAAvE,QAAA,eACZnF,OAAA,CAACvC,KAAK;UAAA0H,QAAA,eACJnF,OAAA,CAACK,WAAW;YAAA,GACNrB,oBAAoB,CAAC,CAAC;YAC1B2K,YAAY,EAAE,CAAC/K,MAAM,CAAC0C,SAAS,CAAC,EAAE1C,MAAM,CAAC2C,OAAO,CAAC,CAAE;YACnD2H,QAAQ,EAAEvH;UAAgB;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNhF,OAAA,CAACxC,GAAG;MAACgM,MAAM,EAAE,EAAG;MAACC,SAAS,EAAC,iBAAiB;MAAAtE,QAAA,EACzCuB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAErD,GAAG,CAAEyC,IAAI,iBACpB9F,OAAA,CAACzC,GAAG;QACFmM,IAAI,EAAE,CAAE;QAERE,OAAO,EAAEA,CAAA,KAAMlI,WAAW,CAACoE,IAAI,CAACc,KAAK,CAAE;QAAAzB,QAAA,eAEvCnF,OAAA,CAAC1C,IAAI;UAACmM,SAAS,EAAEhI,KAAK,KAAKqE,IAAI,CAACc,KAAK,IAAI,QAAS;UAAAzB,QAAA,gBAChDnF,OAAA,CAACxC,GAAG;YAACiM,SAAS,EAAC,MAAM;YAAAtE,QAAA,eACnBnF,OAAA,CAACzC,GAAG;cAAA4H,QAAA,eACFnF,OAAA,CAACG,IAAI;gBAAAgF,QAAA,EAAEzF,CAAC,CAACoG,IAAI,CAACa,KAAK;cAAC;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhF,OAAA,CAACxC,GAAG;YAACgM,MAAM,EAAE,EAAG;YAAArE,QAAA,eACdnF,OAAA,CAACzC,GAAG;cAACmM,IAAI,EAAE,EAAG;cAAAvE,QAAA,eACZnF,OAAA,CAACI,KAAK;gBAACyJ,KAAK,EAAE,CAAE;gBAAA1E,QAAA,EACb,EAACW,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAElC,KAAK,IACT3B,UAAU,CAAC6D,IAAI,CAACe,GAAG,CAAC,GACpBlH,aAAa,CACXsC,UAAU,CAAC6D,IAAI,CAACe,GAAG,CAAC,EACpBpE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,MAAM,EACvBpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,QACnB;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAtBFc,IAAI,CAACa,KAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNhF,OAAA,CAACrB,WAAW;MAACsD,UAAU,EAAEA,UAAW;MAAC6H,UAAU,EAAC;IAAgB;MAAAjF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnEhF,OAAA,CAAC1C,IAAI;MAAA6H,QAAA,gBACHnF,OAAA,CAACvC,KAAK;QAACgM,SAAS,EAAC,6CAA6C;QAAAtE,QAAA,gBAC5DnF,OAAA,CAACtC,UAAU,CAACyC,IAAI;UAAC4J,MAAM;UAACF,KAAK,EAAE,CAAE;UAAA1E,QAAA,EAC9BzF,CAAC,CAAC,UAAU;QAAC;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAClBhF,OAAA,CAACvC,KAAK;UAACgM,SAAS,EAAC,gCAAgC;UAAAtE,QAAA,gBAC/CnF,OAAA,CAAC5B,WAAW;YACV4L,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAQ,CAAE;YAC7BC,YAAY,EAAGC,CAAC,IAAK3H,SAAS,CAAC2H,CAAC;UAAE;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACFhF,OAAA,CAACF,cAAc;YACbsK,YAAY,EAAEhD,UAAW;YACzBiD,WAAW,EAAE3K,CAAC,CAAC,aAAa,CAAE;YAC9B4K,QAAQ,EAAG1D,KAAK,IAAKzF,SAAS,CAACyF,KAAK,CAACA,KAAK,CAAE;YAC5C2D,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAAC;UAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACFhF,OAAA,CAACnC,MAAM;YACLmJ,IAAI,EACFT,OAAO,CAACnE,eAAe,CAACoI,MAAM,CAAC,IAC/B,CAAC,CAAC1J,WAAW,IACb,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,MAAM,GACJ,SAAS,GACT,SACL;YACDuJ,MAAM,EACJlE,OAAO,CAACnE,eAAe,CAACoI,MAAM,CAAC,IAC/B,CAAC,CAAC1J,WAAW,IACb,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,MACH;YACD0I,OAAO,EAAEP,KAAM;YAAAlE,QAAA,EAEdzF,CAAC,CAAC,OAAO;UAAC;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACThF,OAAA,CAACnC,MAAM;YACL6M,IAAI,eAAE1K,OAAA,CAAC3B,qBAAqB;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCjD,OAAO,EAAEO,WAAY;YACrBsH,OAAO,EAAEtB,WAAY;YAAAnD,QAAA,EAEpBzF,CAAC,CAAC,UAAU;UAAC;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACThF,OAAA,CAAClB,aAAa;YAAC+D,OAAO,EAAEA,OAAQ;YAACoC,UAAU,EAAEA;UAAW;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAERhF,OAAA,CAACrC,KAAK;QACJgN,UAAU,EAAE;UACVhI,iBAAiB;UACjBiI,sBAAsB,EAAE,CAAC,GAAG;QAC9B,CAAE;QACFnC,YAAY,EAAEA,YAAa;QAC3B5F,OAAO,EAAEA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgI,MAAM,CAAE/E,IAAI,IAAKA,IAAI,CAACV,OAAO,CAAE;QACjDnB,UAAU,GAAAzD,iBAAA,GAAE0B,WAAW,CAACgB,IAAI,cAAA1C,iBAAA,uBAAhBA,iBAAA,CAAkB0C,IAAK;QACnCoB,MAAM,EAAG1B,GAAG,IAAKA,GAAG,CAAC4B,EAAG;QACxBzC,OAAO,EAAEA,OAAQ;QACjBoC,UAAU,EAAE;UACViE,QAAQ,EAAEhH,QAAQ;UAClB4G,IAAI,EAAE,CAAA9F,WAAW,aAAXA,WAAW,wBAAAzB,kBAAA,GAAXyB,WAAW,CAAEgB,IAAI,cAAAzC,kBAAA,uBAAjBA,kBAAA,CAAmBqK,IAAI,CAAC9C,IAAI,KAAI,CAAC;UACvC+C,KAAK,EAAE7I,WAAW,aAAXA,WAAW,wBAAAxB,kBAAA,GAAXwB,WAAW,CAAEgB,IAAI,cAAAxC,kBAAA,uBAAjBA,kBAAA,CAAmBoK,IAAI,CAACC,KAAK;UACpCC,cAAc,EAAE;QAClB,CAAE;QACF9B,QAAQ,EAAEf,kBAAmB;QAC7BpE,MAAM,EAAE;UACNC,CAAC,EAAE;QACL;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACzE,EAAA,CApaID,cAAc;EAAA,QACD7B,WAAW,EACXc,WAAW,EACXC,WAAW,EAQLd,WAAW,EAM9BA,WAAW,EAKaA,WAAW,EAoNvCW,YAAY,EAIZA,YAAY;AAAA;AAAA4L,EAAA,GA9OR3K,cAAc;AAsapB,eAAeA,cAAc;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}